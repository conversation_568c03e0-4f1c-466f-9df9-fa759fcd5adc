import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:graphql_flutter/graphql_flutter.dart' hide Response;
import 'package:niimbot_cache_manager/src/define/define.dart';
import 'package:niimbot_cache_manager/src/model/font/font_category.dart';
import 'package:niimbot_cache_manager/src/model/font/font_define.dart';
import 'package:niimbot_cache_manager/src/model/font/font_item.dart';
import 'package:niimbot_cache_manager/src/networkApi/network_api.dart';
import 'package:niimbot_cache_manager/src/protocol/font_manager_protocol.dart';
import 'package:niimbot_http/business/services/niimbot_graphql_service.dart';
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/services/niimbot_oss_service.dart';
import 'package:niimbot_http/business/services/niimbot_rest_service.dart';
import 'package:niimbot_http/core/niimbot_http_request_options.dart';
import 'package:niimbot_http/core/niimbot_http_response.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';

class FontManager implements FontManagerProtocol {
  /// 工厂构建
  factory FontManager() => _instance;

  /// 单例
  static final FontManager _instance = FontManager._internal();

  /// 内部构造
  FontManager._internal() {}

  @override
  Future<T?> loadLocalFontCategory<T>({ValueTransformer<T>? transformer}) async {
    try {
      String? fontCategory = NiimbotSp().getString(FontDefine.KEY_FONT_CATEGORY);
      // 缓存字体为空或者首次进入的时候
      if (fontCategory?.isEmpty ?? true) {
        return null;
      }
      dynamic rawFontCategory = json.decode(fontCategory ?? '');
      // 自定义转换器
      if (transformer == null && T is List<FontCategory>) {
        // 启用默认转换器
        transformer ??= FontDefine.fontCategoryTransformer as ValueTransformer<T>?;
      }
      T? result = transformer?.call(rawFontCategory);
      return result;
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return null;
  }

  @override
  Future<T?> loadLocalAllFontList<T>({ValueTransformer<T>? transformer}) async {
    try {
      String? fontList = NiimbotSp().getString(FontDefine.KEY_ALL_FONT_LIST);
      // 缓存字体为空或者首次进入的时候
      if (fontList?.isEmpty ?? true) {
        return null;
      }
      dynamic rawFontList = json.decode(fontList ?? '');
      // 自定义转换器
      if (transformer == null && T is List<FontCategory>) {
        // 启用默认转换器
        transformer ??= FontDefine.fontItemTransformer as ValueTransformer<T>?;
      }
      T? result = transformer?.call(rawFontList);
      return result;
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return null;
  }

  @override
  Future<T?> loadLocalUserFontList<T>({required String userID, ValueTransformer<T>? transformer}) async {
    try {
      String key = '$userID + _ + ${FontDefine.KEY_USER_FONT_LIST}';
      String? userFontList = NiimbotSp().getString(key);
      // 缓存字体为空或者首次进入的时候
      if (userFontList?.isEmpty ?? true) {
        return null;
      }
      dynamic rawFontList = json.decode(userFontList ?? '');
      // 自定义转换器
      if (transformer == null && T is List<FontCategory>) {
        // 启用默认转换器
        transformer ??= FontDefine.fontItemTransformer as ValueTransformer<T>?;
      }
      T? result = transformer?.call(rawFontList);
      return result;
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return null;
  }

  @override
  Future<T?> requestFontCategoryFromNet<T>(
      {ValueTransformer<T>? transformer, ValueUpdateTransformer<T>? updateTransformer}) async {
    // 获取网络字体分类服务
    try {
      NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
          .getService<NiimbotRestService>()
          .client
          .post<dynamic>(FontApi.fontClassifyList.path,
              options: NiimbotHttpRequestOptions(baseUrl: FontApi.fontClassifyList.baseUrl));

      // 解析response
      List<Map<String, dynamic>>? data =
          (netResult.data?['data'] as List<dynamic>?)?.map((e) => Map<String, dynamic>.from(e)).toList();
      ;
      // 保存的值
      String saveString = json.encode(data);
      // 自定义转换器
      if (transformer == null && T is List<FontItem>) {
        // 启用默认转换器
        transformer ??= FontDefine.fontItemTransformer as ValueTransformer<T>?;
      }
      // 获取结果
      T? result = transformer?.call(data);
      // 更新值转换器
      if (updateTransformer != null) {
        // 从本地缓存取出数据
        String? fontCategory = NiimbotSp().getString(FontDefine.KEY_FONT_CATEGORY);
        T? localResult;
        if (fontCategory?.isNotEmpty ?? false) {
          dynamic rawFontCategory = json.decode(fontCategory ?? '');
          // 序列化本地数据
          localResult = transformer?.call(rawFontCategory);
        }
        // 更新值转换器获取最终值
        T? updateData = updateTransformer(localValue: localResult, newValue: result);
        // 转换为String
        saveString = json.encode(updateData);
      }
      // 存储结果
      await NiimbotSp().setString(FontDefine.KEY_FONT_CATEGORY, saveString);
      // 返回网络返回值
      return result;
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return null;
  }

  @override
  Future<T?> requestFontListFromNet<T>(
      {required Map<String, dynamic> params,
      ValueTransformer<T>? transformer,
      ValueUpdateTransformer<T>? updateTransformer}) async {
    // 获取网络字体分类服务
    try {
      NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
          .getService<NiimbotOSSService>()
          .client
          .get<dynamic>(FontApi.fontList.path, options: NiimbotHttpRequestOptions(baseUrl: FontApi.fontList.baseUrl));

      // 解析response
      List<Map<String, dynamic>>? data =
          netResult.data?.map<Map<String, dynamic>>((e) => Map<String, dynamic>.from(e)).toList();
      // 保存的值
      String saveString = json.encode(data);
      // 自定义转换器
      if (transformer == null && T is List<FontItem>) {
        // 启用默认转换器
        transformer ??= FontDefine.fontItemTransformer as ValueTransformer<T>?;
      }
      // 获取结果
      T? result = transformer?.call(data);
      // 更新值转换器
      if (updateTransformer != null) {
        // 从本地缓存取出数据
        String? fontList = NiimbotSp().getString(FontDefine.KEY_ALL_FONT_LIST);
        T? localResult;
        if (fontList?.isNotEmpty ?? false) {
          dynamic rawFontList = json.decode(fontList ?? '');
          // 序列化本地数据
          localResult = transformer?.call(rawFontList);
        }
        // 更新值转换器获取最终值
        T? updateData = updateTransformer(localValue: localResult, newValue: result);
        // 转换为String
        saveString = json.encode(updateData);
      }
      // 存储结果
      await NiimbotSp().setString(FontDefine.KEY_ALL_FONT_LIST, saveString);
      // 返回网络返回值
      return result;
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return null;
  }

  @override
  Future<T?> requestUserFontListFromNet<T>(
      {required String userID, ValueTransformer<T>? transformer, ValueUpdateTransformer<T>? updateTransformer}) async {
    /// 获取网络字体分类服务
    String hql = '''
    query getUserFonts { findUserFonts { id name path status fileName code thumbnailUrl type isVip priority classifyId classifyName advertisementImageUrl agreementName agreementUrl copyright usageDatetime } }
    ''';
    QueryOptions<List<Map<String, dynamic>>?> options = QueryOptions<List<Map<String, dynamic>>?>(
        document: gql(hql),
        variables: {},
        parserFn: (Map<String, dynamic> data) {
          List<dynamic>? fonts = data['findUserFonts'];
          return fonts?.map((e) => Map<String, dynamic>.from(e)).toList();
        });
    QueryResult<List<Map<String, dynamic>>?> netResult =
        await NiimbotHttpManager.getDefault().getService<NiimbotGraphQLService>().client.gqlClient.query(options);

    try {
      /// 解析response
      List<Map<String, dynamic>>? data = netResult.parsedData;
      // 自定义转换器
      if (transformer == null && T is List<FontItem>) {
        // 启用默认转换器
        transformer ??= FontDefine.fontItemTransformer as ValueTransformer<T>?;
      }
      // 获取结果
      T? result = transformer?.call(data);
      // 保存的值
      String saveString = json.encode(data);
      // 从本地缓存取出数据
      String key = '$userID + _ + ${FontDefine.KEY_USER_FONT_LIST}';
      // 更新值转换器
      if (updateTransformer != null) {
        String? fontList = NiimbotSp().getString(key);
        T? localResult;
        if (fontList?.isNotEmpty ?? false) {
          dynamic rawFontList = json.decode(fontList ?? '');
          // 序列化本地数据
          localResult = transformer?.call(rawFontList);
        }
        // 更新值转换器获取最终值
        T? updateData = updateTransformer(localValue: localResult, newValue: result);
        // 转换为String
        saveString = json.encode(updateData);
      }
      // 存储结果
      await NiimbotSp().setString(key, saveString);
      // 返回网络返回值
      return result;
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return null;
  }

  @override
  Future<bool> updateUserFont({required String userID, required String value}) async {
    // 获取Key
    String key = '$userID + _ + ${FontDefine.KEY_USER_FONT_LIST}';
    // 存储结果
    return await NiimbotSp().setString(key, value);
  }

  @override
  Future<bool> clearFontCache() async {
    var keys = NiimbotSp().getKeys();
    List<String> userFontList = keys.where((element) => element.endsWith(FontDefine.KEY_USER_FONT_LIST)).toList();
    List<Future<bool>> result = [FontDefine.KEY_FONT_CATEGORY, FontDefine.KEY_ALL_FONT_LIST, ...userFontList]
        .map((key) async => await NiimbotSp().remove(key))
        .toList();
    List<bool> isClearResult = await Future.wait(result.map((e) async => (await e) == true));
    return isClearResult.where((element) => element == false).isEmpty;
  }

  @override
  Future<bool> clearFontListCache() async {
    var keys = NiimbotSp().getKeys();
    List<String> userFontList = keys.where((element) => element.endsWith(FontDefine.KEY_USER_FONT_LIST)).toList();
    List<Future<bool>> result =
        [FontDefine.KEY_ALL_FONT_LIST, ...userFontList].map((key) async => await NiimbotSp().remove(key)).toList();
    List<bool> isClearResult = await Future.wait(result.map((e) async => (await e) == true));
    return isClearResult.where((element) => element == false).isEmpty;
  }

  @override
  Future<bool> clearFontCategoryCache() async {
    List<Future<bool>> result = [
      FontDefine.KEY_FONT_CATEGORY,
    ].map((key) async => await NiimbotSp().remove(key)).toList();
    List<bool> isClearResult = await Future.wait(result.map((e) async => (await e) == true));
    return isClearResult.where((element) => element == false).isEmpty;
  }
}
