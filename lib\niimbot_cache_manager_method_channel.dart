import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'niimbot_cache_manager_platform_interface.dart';

/// An implementation of [NiimbotCacheManagerPlatform] that uses method channels.
class MethodChannelNiimbotCacheManager extends NiimbotCacheManagerPlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('niimbot_cache_manager');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>('getPlatformVersion');
    return version;
  }
}
