/// Consumable spec model
class ConsumableSpec {
  /// Spec ID
  String? id;

  /// Spec name
  String? name;

  /// Height of the label
  num? height;

  /// Margins [top, right, bottom, left]
  List<num>? margin;

  /// Size value
  String? size;

  /// Preview image URL
  String? previewImageUrl;

  /// Created date
  String? createdAt;

  /// Modified date
  String? modifiedAt;

  ConsumableSpec({
    this.id,
    this.name,
    this.height,
    this.margin,
    this.size,
    this.previewImageUrl,
    this.createdAt,
    this.modifiedAt,
  });

  ConsumableSpec.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    name = json["name"];
    height = json["height"];
    margin = json["margin"] != null ? List<num>.from(json["margin"]) : null;
    size = json["size"];
    previewImageUrl = json["previewImageUrl"];
    createdAt = json["createdAt"];
    modifiedAt = json["modifiedAt"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["id"] = id;
    data["name"] = name;
    data["height"] = height;
    if (margin != null) {
      data["margin"] = margin;
    }
    data["size"] = size;
    data["previewImageUrl"] = previewImageUrl;
    data["createdAt"] = createdAt;
    data["modifiedAt"] = modifiedAt;
    return data;
  }
}

/// Consumable category model
class ConsumableCategory {
  /// Category name
  String? name;

  /// Size value as number
  num? size;

  /// Indicates if this category is recommended
  bool? isRecommended;

  /// List of label specifications
  List<ConsumableSpec>? labels;

  ConsumableCategory({
    this.name,
    this.size,
    this.labels,
    this.isRecommended,
  });

  ConsumableCategory.fromJson(Map<String, dynamic> json) {
    name = json["name"];
    size = json["size"];
    isRecommended = json["isRecommended"];

    if (json["labels"] != null) {
      labels = <ConsumableSpec>[];
      json["labels"].forEach((v) {
        labels!.add(ConsumableSpec.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["name"] = name;
    data["size"] = size;
    data["isRecommended"] = isRecommended;

    if (labels != null) {
      data["labels"] = labels!.map((v) => v.toJson()).toList();
    }

    return data;
  }
}
