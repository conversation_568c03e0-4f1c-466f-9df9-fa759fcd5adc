//
//  Generated file. Do not edit.
//

// clang-format off

#include "generated_plugin_registrant.h"

#include <connectivity_plus/connectivity_plus_windows_plugin.h>
#include <isar_flutter_libs/isar_flutter_libs_plugin.h>
#include <niimbot_cache_manager/niimbot_cache_manager_plugin_c_api.h>

void RegisterPlugins(flutter::PluginRegistry* registry) {
  ConnectivityPlusWindowsPluginRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("ConnectivityPlusWindowsPlugin"));
  IsarFlutterLibsPluginRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("IsarFlutterLibsPlugin"));
  NiimbotCacheManagerPluginCApiRegisterWithRegistrar(
      registry->GetRegistrarForPlugin("NiimbotCacheManagerPluginCApi"));
}
