import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';

/// [GrayConfigManagerProtocol] 灰度配置缓存协议
/// 包含网络请求，缓存管理等
abstract class GrayConfigManagerProtocol {
  /// 加载灰度配置
  Future<T?> loadLocalGrayConfig<T>(
      {required String uid, ValueTransformer<T>? transformer});

  /// 从网络请求灰度配置
  Future<Map<String, dynamic>> requestGrayConfigFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer});
}