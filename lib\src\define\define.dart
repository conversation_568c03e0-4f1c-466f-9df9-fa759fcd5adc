/// 值转换器
typedef ValueTransformer<T> = T? Function(dynamic);

/// 值更新转换器
typedef ValueUpdateTransformer<T> = T? Function(
    {required T? localValue, required T? newValue});

/// Json转化器
typedef JsonTransformer<T> = dynamic Function({required T? value});

/// 环境变量
enum AppEnvironment {
  /// 生产环境
  production,

  /// 测试环境
  test,
}

/// 触发时机
enum TriggerTime {
  /// 启动App
  startApp,

  /// 切换语言
  languageChange,

  /// 登录态变化
  loginStatusChange,

  /// VIP购买
  vipChange
}
