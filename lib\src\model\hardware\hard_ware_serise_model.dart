import 'dart:convert';
import 'dart:io';

HardWareSeriesModel hardWareSeriseModelFromJson(String str) => HardWareSeriesModel.fromJson(json.decode(str));

String hardWareSeriseModelToJson(HardWareSeriesModel data) => json.encode(data.toJson());

class HardWareSeriesModel {
  HardWareSeriesModel({
    this.id,
    this.name,
    this.image,
    this.orderNo,
    this.createTime,
    this.hardwareNameStr,
    this.hardwareIdStr,
    this.helpVideo,
  });

  final String? id;
  final String? name;
  final String? image;
  final int? orderNo;
  final String? createTime;
  final String? hardwareNameStr;
  final String? hardwareIdStr;
  final String? helpVideo;

  List<String>? get hardwareIdList {
    return hardwareIdStr?.split(',');
  }

  factory HardWareSeriesModel.fromJson(Map<String, dynamic> json) => HardWareSeriesModel(
        id: json["id"].toString(),
        name: json["name"],
        image: json["image"],
        orderNo: json["orderNo"],
        createTime: json["createTime"],
        hardwareNameStr: json["hardwareNameStr"],
        hardwareIdStr: Platform.isAndroid ? json["hardware_series_id"] : json["hardwareIdStr"],
        helpVideo: json["helpVideo"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image": image,
        "orderNo": orderNo,
        "createTime": createTime,
        "hardwareNameStr": hardwareNameStr,
        "hardwareIdStr": hardwareIdStr,
        "helpVideo": helpVideo,
      };
}
