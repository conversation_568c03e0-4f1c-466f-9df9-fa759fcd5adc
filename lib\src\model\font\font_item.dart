import 'dart:convert';
import 'dart:io';
import 'dart:ui';

FontItem fontItemFromJson(String str) => FontItem.fromJson(json.decode(str));

String fontItemToJson(FontItem data) => json.encode(data.toJson());

//某个特定字体记录
class FontItem {
  int? id;
  int? lpId;
  String? name;
  String? path;
  String? publishTime;
  int? status;
  String? fileName;
  String? code;
  String? thumbnailUrl;
  int? type;
  bool? isVip;
  String? agreementUrl;
  String? agreementName;
  String? copyright;
  String? advertisementImageUrl;
  String? classifyId;
  String? classifyName;
  int? priority;
  int? showNewestVersion;
  String? usageDatetime;

  /// 预览图宽高
  Size? thumbnailImageSize;
  int? downloadTime;

  /// 本次文件存储使用的文件名
  String? _localFileName;

  get localFileName {
    if (_localFileName == null) {
      String suffixFileName = ".ttf";
      if (Platform.isIOS) {
        suffixFileName = _getSuffixFileName(path ?? '');
      }
      _localFileName = '$code$suffixFileName';
    }
    return _localFileName;
  }

  FontItem({
    this.id,
    this.lpId,
    this.name,
    this.path,
    this.publishTime,
    this.status,
    this.fileName,
    this.code,
    this.thumbnailUrl,
    this.type,
    this.isVip,
    this.agreementUrl,
    this.agreementName,
    this.copyright,
    this.advertisementImageUrl,
    this.classifyId,
    this.classifyName,
    this.priority,
    this.showNewestVersion,
    this.usageDatetime,
    this.downloadTime = 0,
  });

  FontItem.fromJson(dynamic json) {
    id = json['id'];
    lpId = json['lpId'];
    name = json['name'];
    path = json['path'];
    publishTime = json['publishTime'];
    status = json['status'];
    fileName = json['fileName'];
    code = json['code'];
    thumbnailUrl = json['thumbnailUrl'];
    type = json['type'];
    isVip = json['isVip'];
    agreementUrl = json['agreementUrl'];
    agreementName = json['agreementName'];
    copyright = json['copyright'];
    advertisementImageUrl = json['advertisementImageUrl'];
    classifyId = json['classifyId'];
    classifyName = json['classifyName'];
    priority = json['priority'];
    showNewestVersion = json['showNewestVersion'];
    usageDatetime = json['usageDatetime'];
    downloadTime = json["downloadTime"] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['lpId'] = lpId;
    map['name'] = name;
    map['path'] = path;
    map['publishTime'] = publishTime;
    map['status'] = status;
    map['fileName'] = fileName;
    map['code'] = code;
    map['thumbnailUrl'] = thumbnailUrl;
    map['type'] = type;
    map['isVip'] = isVip;
    map['agreementUrl'] = agreementUrl;
    map['agreementName'] = agreementName;
    map['copyright'] = copyright;
    map['advertisementImageUrl'] = advertisementImageUrl;
    map['classifyId'] = classifyId;
    map['classifyName'] = classifyName;
    map['priority'] = priority;
    map['showNewestVersion'] = showNewestVersion;
    map['usageDatetime'] = usageDatetime;
    map['downloadTime'] = downloadTime;
    return map;
  }

  bool hasCopyRight() {
    return copyright != null && (copyright?.isNotEmpty ?? false);
  }

  bool hasVip() {
    return isVip != null && isVip!;
  }

  bool operator ==(item) {
    return (item is FontItem) && item.code == code;
  }

  String _getSuffixFileName(String url) {
    int lastIndex = url.lastIndexOf(".");
    if (lastIndex < 0) {
      return "";
    }
    String fileNameSuffix = url.substring(lastIndex);
    return fileNameSuffix;
  }
}
