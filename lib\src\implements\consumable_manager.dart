import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_cache_manager/src/model/consumable/consumable_define.dart';
import 'package:niimbot_cache_manager/src/model/consumable/consumable_model.dart';
import 'package:niimbot_cache_manager/src/networkApi/network_api.dart';
import 'package:niimbot_cache_manager/src/networkApi/network_listen.dart';
import 'package:niimbot_cache_manager/src/protocol/consumable_manager_protocol.dart';
import 'package:niimbot_http/niimbot_http.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';
import 'package:tuple/tuple.dart';

/// Implementation of the [ConsumableManagerProtocol]
class ConsumableManager implements ConsumableManagerProtocol {
  /// Singleton factory
  factory ConsumableManager() => _instance;

  /// Private singleton instance
  static final ConsumableManager _instance = ConsumableManager._internal();

  /// Private constructor
  ConsumableManager._internal() {}

  @override
  Future<bool> clearConsumableCache() async {
    bool fail = false;
    final keys = NiimbotSp().getKeys();
    for (final key in keys) {
      if (key.startsWith(ConsumableDefine.Consumable_Spec_List_Key_Prefix)) {
        bool result = await NiimbotSp().remove(key);
        if (!result) {
          fail = true;
        }
      }
    }
    return !fail;
  }

  @override
  Future<T?> loadLocalConsumableSpecs<T>({
    required String consumableCode,
    ValueTransformer<T>? transformer,
  }) async {
    String saveKey = ConsumableDefine.getConsumableSpecListKey(consumableCode);

    if (transformer == null && T == List<ConsumableCategory>) {
      transformer =
      ConsumableDefine.consumableSpecTransformer as ValueTransformer<T>?;
    }

    String? specsJson = NiimbotSp().getString(saveKey);
    if (specsJson.isEmpty) {
      // Load from JSON file based on environment
      try {
        final String assetPath = NetworkApi.appEnv == AppEnvironment.test
            ? "packages/niimbot_cache_manager/assets/c1_consumableCode_test.json"
            : "packages/niimbot_cache_manager/assets/c1_consumableCode.json";

        final String jsonString = await rootBundle.loadString(assetPath);
        final Map<String, dynamic> jsonData = json.decode(jsonString);

        // Get the data for the specific consumableCode
        if (jsonData.containsKey(consumableCode)) {
          final dynamic data = jsonData[consumableCode];

          // Return transformed data
          if (transformer != null) {
            return transformer(data);
          }
          return data as T?;
        }
      } catch (e) {
        debugPrint('Error loading consumable specs from assets: $e');
      }
      return null;
    }

    try {
      final dynamic decoded = json.decode(specsJson);
      if (transformer != null) {
        return transformer(decoded);
      }
      return decoded as T?;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<T?> requestConsumableSpecsFromNet<T>({
    required String consumableCode,
    ValueTransformer<T>? transformer,
    JsonTransformer<T>? jsonTransformer,
  }) async {
    bool isNetworkConnect = await NetWorkListener.checkNetworkConnected();
    if (!isNetworkConnect) {
      return null;
    }

    try {
      var request = NiimbotHttpRequest(
        path: ConsumableApi.consumableSpecList.path,
        method: "GET",
        queryParams: {"consumableCode": consumableCode},
        options: NiimbotHttpRequestOptions(
          baseUrl: ConsumableApi.consumableSpecList.baseUrl,
          userAgent: NiimbotHttpManager
              .getDefault()
              .getService<NiimbotRestService>()
              .client
              .config
              .userAgent,
          sendTimeout: const Duration(seconds: 2),
          receiveTimeout: const Duration(seconds: 2),
        ),
      );

      NiimbotHttpResponse<dynamic> netResult =
      await NiimbotHttpManager
          .getDefault()
          .getService<NiimbotRestService>()
          .client
          .request<dynamic>(request);

      int? statusCode = netResult.statusCode;
      dynamic result = netResult.data;

      if ((statusCode == 200 || statusCode == 0 || statusCode == 1) &&
          result != null &&
          result is Map<String, dynamic>) {
        final data = result["data"];

        // 保存的值
        String dataJson = json.encode(data);

        // 自定义转换器
        if (transformer == null && T == List<ConsumableCategory>) {
          transformer = ConsumableDefine.consumableSpecTransformer
          as ValueTransformer<T>?;
        }

        // 获取结果
        T? resultData = transformer?.call(data);

        // 更新缓存处理
        if (jsonTransformer != null && resultData != null) {
          String saveKey =
          ConsumableDefine.getConsumableSpecListKey(consumableCode);
          await NiimbotSp().setString(saveKey, dataJson);
        } else {
          // 直接保存原始数据
          String saveKey =
          ConsumableDefine.getConsumableSpecListKey(consumableCode);
          await NiimbotSp().setString(saveKey, dataJson);
        }

        // 返回转换后的数据
        return resultData;
      }

      return null;
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
      return null;
    }
  }

  @override
  Future<bool> saveConsumableSpecs<T>({
    required String consumableCode,
    required T specs,
    required JsonTransformer<T> jsonTransformer,
  }) async {
    String saveKey = ConsumableDefine.getConsumableSpecListKey(consumableCode);

    try {
      // 使用命名参数 value
      final jsonData = jsonTransformer(value: specs);
      final specsJson = json.encode(jsonData);

      await NiimbotSp().setString(saveKey, specsJson);
      return true;
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
      return false;
    }
  }

  /// Save consumable data directly from a JSON string
  /// Used primarily for testing or direct data updates
  Future<bool> saveConsumableDataFromJson({
    required String consumableCode,
    required String jsonData,
  }) async {
    String saveKey = ConsumableDefine.getConsumableSpecListKey(consumableCode);

    try {
      // Validate JSON by parsing it
      final dynamic data = json.decode(jsonData);

      // Save the validated JSON data
      await NiimbotSp().setString(saveKey, jsonData);
      return true;
    } catch (e, s) {
      debugPrint('Error saving consumable data from JSON: $e');
      debugPrint('Stack trace:\n $s');
      return false;
    }
  }

  @override
  Future<Tuple3<ConsumableCategory?, ConsumableSpec?, bool>>
  findConsumableSpecById({
    required String consumableCode,
    required String specId,
    bool preferLocalData = false,
  }) async {
    try {
      List<ConsumableCategory>? categories;

      // Check if we should try local data first
      if (preferLocalData) {
        // Try to load from local cache first
        categories = await loadLocalConsumableSpecs<List<ConsumableCategory>>(
          consumableCode: consumableCode,
        );

        // If found in local cache, use that data
        if (categories != null && categories.isNotEmpty) {
          // Search for the spec locally and return if found
          for (ConsumableCategory category in categories) {
            if (category.labels != null && category.labels!.isNotEmpty) {
              ConsumableSpec? spec;
              try {
                spec = category.labels!.firstWhere(
                      (label) => label.id == specId,
                  orElse: () => throw Exception('Spec not found'),
                );
                // Found locally, return immediately
                return Tuple3<ConsumableCategory?, ConsumableSpec?, bool>(
                    category, spec, true);
              } catch (_) {
                // Continue searching in other categories
                continue;
              }
            }
          }
        }
      }

      // If not found locally or preferLocalData is false, try network
      bool isNetworkConnected = await NetWorkListener.checkNetworkConnected();
      if (isNetworkConnected) {
        // Try to get data from network
        categories =
        await requestConsumableSpecsFromNet<List<ConsumableCategory>>(
          consumableCode: consumableCode,
        );
      }

      // If network request failed or network is unavailable, try to load from local
      // (only if we haven't tried local data already)
      if ((categories == null || categories.isEmpty) && !preferLocalData) {
        categories = await loadLocalConsumableSpecs<List<ConsumableCategory>>(
          consumableCode: consumableCode,
        );

        // If no data found in local cache either, return not found
        if (categories == null || categories.isEmpty) {
          return Tuple3<ConsumableCategory?, ConsumableSpec?, bool>(
              null, null, false);
        }
      }

      // Search through all categories and their specs for the matching specId
      for (ConsumableCategory category in categories!) {
        if (category.labels != null && category.labels!.isNotEmpty) {
          // Try to find the spec with matching ID in this category
          ConsumableSpec? spec;
          try {
            spec = category.labels!.firstWhere(
                  (label) => label.id == specId,
              orElse: () => throw Exception('Spec not found'),
            );
          } catch (_) {
            // Spec not found in this category, continue to the next one
            continue;
          }

          // If found, return the category, spec, and success
          return Tuple3<ConsumableCategory?, ConsumableSpec?, bool>(
              category, spec, true);
        }
      }

      // Spec not found in any category
      return Tuple3<ConsumableCategory?, ConsumableSpec?, bool>(
          null, null, false);
    } catch (e, s) {
      debugPrint('Error finding consumable spec by ID: $e');
      debugPrint('Stack trace:\n $s');
      return Tuple3<ConsumableCategory?, ConsumableSpec?, bool>(
          null, null, false);
    }
  }
}
