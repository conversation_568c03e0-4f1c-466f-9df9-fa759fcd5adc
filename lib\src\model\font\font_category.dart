import 'dart:convert';

FontCategory fontCategoryFromJson(String str) => FontCategory.fromJson(json.decode(str));

String fontCategoryToJson(FontCategory data) => json.encode(data.toJson());

class FontCategory {
  String? id;
  String? name;
  String? languageCode;
  int? showNewestVersion;

  FontCategory({
    this.id,
    this.name,
    this.languageCode,
    this.showNewestVersion,
  });

  FontCategory.fromJson(dynamic json) {
    id = json['id'].toString();
    name = json['name'];
    languageCode = json['languageCode'];
    showNewestVersion = json["showNewestVersion"];
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['name'] = name;
    map['languageCode'] = languageCode;
    map["showNewestVersion"] = showNewestVersion;
    return map;
  }
}
