import 'dart:io';

import 'package:cider/cider.dart';
import 'package:cider/src/cli/config.dart';
import 'package:path/path.dart';
import 'package:stack_trace/stack_trace.dart';

const typeFlag = 'type';

main(List<String> arguments) async {
  final project = Project(findProjectRoot(), Config());
  final version = await project.getVersion();
  stdout.writeln("****Building, please wait....****");

  stdout.writeln("****1.Publish Package....****");
  await startProcess('dart', ['pub', 'publish', '-f']);

  stdout.writeln("****2.Git commit version number....****");
  await startProcess('git', ['commit', '-a', '-m', 'release:发布 $version']);
  await startProcess('git', ['tag', version.toString()]);
  await startProcess('git', ['push', '--tags']);
  await startProcess('git', ['push']);
}

///运行命令行
Future<void> startProcess(String executable, List<String> args) async {
  stdout.writeln('Running "$executable ${args.join(' ')}"');
  final process = await Process.start(executable, args,
      workingDirectory: findProjectRoot(), runInShell: true);
  process.stdout.listen(
    (List<int> event) {
      stdout.add(event);
    },
  );
  process.stderr.listen(
    (List<int> event) {
      stderr.add(event);
    },
  );
  final int exitCode = await process.exitCode;
  if (exitCode != 0) {
    stderr.writeln(
        'Running "$executable ${args.join(' ')}" failed with $exitCode.');
    exit(exitCode);
  }
}

String findProjectRoot() {
  final frame = Frame.caller(1);
  final rootDir = dirname(frame.uri.path.substring(1, frame.uri.path.length));
  return dirname(rootDir);
}
