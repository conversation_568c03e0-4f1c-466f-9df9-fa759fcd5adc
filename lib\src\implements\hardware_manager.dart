import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:niimbot_cache_manager/src/define/define.dart';
import 'package:niimbot_cache_manager/src/model/hardware/hard_ware_serise_model.dart';
import 'package:niimbot_cache_manager/src/model/hardware/hardware_define.dart';
import 'package:niimbot_cache_manager/src/model/hardware/hardware_list_model.dart';
import 'package:niimbot_cache_manager/src/networkApi/network_api.dart';
import 'package:niimbot_cache_manager/src/protocol/hardware_manager_protocol.dart';
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/services/niimbot_rest_service.dart';
import 'package:niimbot_http/core/niimbot_http_request.dart';
import 'package:niimbot_http/core/niimbot_http_request_options.dart';
import 'package:niimbot_http/core/niimbot_http_response.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';
import 'package:path_provider/path_provider.dart';

class HardWareManager implements HardWareManagerProtocol {
  @override
  Future<T?> getPrinterInfo<T>(
      {ValueTransformer<T>? transformer,
      ValueUpdateTransformer<T>? updateTransformer,
      Function(List<Map<String, dynamic>>?, String result)? requestResultCall,
      String? defaultPrinterInfo}) async {
    try {
      // 优先使用缓存
      String? localHardwareList = NiimbotSp().getString(HardWareDefine.hardwareList);

      // 缓存不存在, 使用默认配置写入缓存
      if (localHardwareList == null || localHardwareList.isEmpty) {
        String config = (defaultPrinterInfo ?? "").isNotEmpty
            ? defaultPrinterInfo!
            : await rootBundle.loadString(
                "packages/niimbot_cache_manager/assets/printerList${NetworkApi.appEnv == AppEnvironment.production ? '' : '_test'}.json");
        if (config.isNotEmpty) {
          await NiimbotSp().setString(HardWareDefine.hardwareList, config);
          localHardwareList = config;
        }
      }

      // 保存的值
      String? saveString = localHardwareList;
      var localHardwareListJson = json.decode(localHardwareList ?? '');
      // 自定义转换器
      if (transformer == null && T is HardwareModelList) {
        // 启用默认转换器
        transformer ??= HardWareDefine.hardwareModelListTransformer as ValueTransformer<T>?;
      }
      // 获取本地序列化结果
      T? localResult = transformer?.call(localHardwareListJson);

      // 获取硬件列表
      try {
        var request = NiimbotHttpRequest(
            path: HardWareApi.hardwareList.path,
            method: "GET",
            options: NiimbotHttpRequestOptions(
                baseUrl: HardWareApi.hardwareList.baseUrl,
                userAgent: NiimbotHttpManager.getDefault().getService<NiimbotRestService>().client.config.userAgent));
        final directory = await getTemporaryDirectory();
        final newDirectory = Directory('${directory.path}/deviceInfo');
        if (!await newDirectory.exists()) {
          await newDirectory.create();
        }
        String savePath = "${newDirectory.path}/deviceInfo.json";
        NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
            .getService<NiimbotRestService>()
            .client
            .download("${HardWareApi.hardwareList.baseUrl}${HardWareApi.hardwareList.path}", savePath);
        File deviceInfoFile = File(savePath);
        String fileContents = "";
        if (await deviceInfoFile.exists()) {
          // 读取文件中的内容并返回
          fileContents = await deviceInfoFile.readAsString();
        }
        debugPrint("printerListRequest:------------------success-------------------\n printList:$fileContents");
        // 自定义转换器
        if (transformer == null && T is HardwareModelList) {
          // 启用默认转换器
          transformer ??= HardWareDefine.hardwareModelListTransformer as ValueTransformer<T>?;
        }
        // 解析response
        List<Map<String, dynamic>>? data =
            (jsonDecode(fileContents) as List<dynamic>).map((e) => Map<String, dynamic>.from(e)).toList();
        // 转换为String
        requestResultCall?.call(data, "success");
        saveString = json.encode(data);
        T? result = transformer?.call(data);
        // 更新值转换器
        if (updateTransformer != null) {
          // 更新值转换器获取最终值
          T? updateData = updateTransformer(localValue: localResult, newValue: result);
          // 转换为String
          saveString = json.encode(updateData);
        }
        // 存储结果
        await NiimbotSp().setString(HardWareDefine.hardwareList, saveString ?? '');
        return result;
      } catch (e, s) {
        debugPrint("printerListRequest:------------------failed-------------------");
        debugPrint('Exception details:\n $e');
        debugPrint('Stack trace:\n $s');
        requestResultCall?.call([], e.toString());
      }
      return localResult;
    } catch (e, s) {
      debugPrint("printerListRequest:------------------failed-------------------");
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return null;
  }

  @override
  Future<T?> getPrinterSeriesInfo<T>({ValueTransformer<T>? transformer}) async {
    // 获取硬件系列列表
    // 赋予baseURL
    NiimbotHttpManager.getDefault().getService<NiimbotRestService>().client.dioClient.options.baseUrl =
        HardWareApi.deviceSeriesList.baseUrl;
    try {
      NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
          .getService<NiimbotRestService>()
          .client
          .request<dynamic>(NiimbotHttpRequest(
              method: 'POST',
              path: HardWareApi.deviceSeriesList.path,
              options: NiimbotHttpRequestOptions(baseUrl: HardWareApi.deviceSeriesList.baseUrl)));

      // 解析response
      List<Map<String, dynamic>>? data =
          (netResult.data['data'] as List<dynamic>).map((e) => Map<String, dynamic>.from(e)).toList();

      // 自定义转换器
      if (transformer == null && T is List<HardWareSeriesModel>) {
        // 启用默认转换器
        transformer ??= HardWareDefine.hardWareSeriesModelTransformer as ValueTransformer<T>?;
      }
      T? result = transformer?.call(data);
      return result;
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return null;
  }
}
