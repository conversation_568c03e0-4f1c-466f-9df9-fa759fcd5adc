import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_cache_manager/src/model/material/material_define.dart';
import 'package:tuple/tuple.dart';

/// [MaterialManagerProtocol] 素材缓存协议
/// 包含网络请求，缓存管理等
abstract class MaterialManagerProtocol {
  /// 加载图标分类
  Future<T?> loadLocalIconCategory<T>(
      {required String languageCode, ValueTransformer<T>? transformer});

  /// 加载所有的图标列表分组
  /// [industryCategoryIdList] List<Tuple2<industryId, categoryId>>
  Future<List<T>> loadLocalAllIconListGroup<T>(
      {required List<Tuple2<int, int>> industryCategoryIdList,
      required ValueTransformer<T> transformer});

  /// 加载边框分类
  Future<T?> loadLocalBorderCategory<T>(
      {required String languageCode, ValueTransformer<T>? transformer});

  /// 加载所有的边框列表分组
  /// [industryCategoryIdList] List<Tuple2<industryId, categoryId>>
  Future<List<T>> loadLocalAllBorderListGroup<T>(
      {required List<Tuple2<int, int>> industryCategoryIdList,
      required ValueTransformer<T> transformer});

  /// 获取所有vip图标id列表
  Future<List<int>> getIconVipIdList<T>(
      {bool requestFromNet = false,
      Map<String, dynamic>? extra,
      RefreshGroupListVipStatus? refreshGroupListVipStatus});

  /// 获取所有vip线框id列表
  Future<List<int>> getBorderVipIdList<T>(
      {bool requestFromNet = false,
      Map<String, dynamic>? extra,
      RefreshGroupListVipStatus? refreshGroupListVipStatus});

  /// 保存所有vip图标id列表
  Future<bool> saveIconVipIdList({required List<int> vipIdList});

  /// 保存所有vip线框id列表
  Future<bool> saveBorderVipIdList({required List<int> vipIdList});

  /// 从网络请求图标分类
  Future<Map<String, dynamic>> requestIconCategoryFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer});

  /// 从网络请求线框分类
  Future<Map<String, dynamic>> requestBorderCategoryFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer});

  /// 从网络请求图标列表
  Future<Map<String, dynamic>> requestIconListFromNet<T>(
      {Map<String, dynamic>? params,
      ValueTransformer<T>? transformer,
      JsonTransformer<T>? jsonTransformer});

  /// 从网络请求线框列表
  Future<Map<String, dynamic>> requestBorderListFromNet<T>(
      {Map<String, dynamic>? params,
      ValueTransformer<T>? transformer,
      JsonTransformer<T>? jsonTransformer});

  /// 从缓存加载图标列表分组
  T? loadIconGroup<T>(
      {required int industryId,
      required int categoryId,
      required ValueTransformer<T> transformer});

  /// 从缓存加载线框列表分组
  T? loadBorderGroup<T>(
      {required int industryId,
      required int categoryId,
      required ValueTransformer<T> transformer});

  /// 更新图标列表分组
  Future<bool> saveIconGroup<T>(
      {required T iconGroup,
      required JsonTransformer jsonTransformer,
      Map<String, dynamic>? extra});

  /// 更新线框列表分组
  Future<bool> saveBorderGroup<T>(
      {required T borderGroup,
      required JsonTransformer<T> jsonTransformer,
      Map<String, dynamic>? extra});

  /// 清空所有的素材缓存
  Future<bool> clearMaterialCache();

  /// 清空所有的素材列表缓存
  Future<bool> clearMaterialListCache();

  /// 清空所有的素材分类缓存
  Future<bool> clearMaterialCategoryCache();
}
