import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_cache_manager/src/model/consumable/consumable_model.dart';

class ConsumableDefine {
  /// Cache key prefixes
  static const String Consumable_Spec_List_Key_Prefix = "consumable_spec_list";

  /// Default transformer for consumable specs
  static ValueTransformer<List<ConsumableCategory>?> consumableSpecTransformer =
      (dynamic rawValue) {
    List<ConsumableCategory>? result = [];
    if (rawValue is List) {
      for (var item in rawValue) {
        if (item is Map<String, dynamic>) {
          result.add(ConsumableCategory.fromJson(item));
        }
      }
    }
    return result;
  };

  /// Build cache key for consumable specs based on consumableCode
  static String getConsumableSpecListKey(String consumableCode) {
    return "${Consumable_Spec_List_Key_Prefix}_$consumableCode";
  }
}
