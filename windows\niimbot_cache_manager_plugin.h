#ifndef FLUTTER_PLUGIN_NIIMBOT_CACHE_MANAGER_PLUGIN_H_
#define FLUTTER_PLUGIN_NIIMBOT_CACHE_MANAGER_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace niimbot_cache_manager {

class NiimbotCacheManagerPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  NiimbotCacheManagerPlugin();

  virtual ~NiimbotCacheManagerPlugin();

  // Disallow copy and assign.
  NiimbotCacheManagerPlugin(const NiimbotCacheManagerPlugin&) = delete;
  NiimbotCacheManagerPlugin& operator=(const NiimbotCacheManagerPlugin&) = delete;

 private:
  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace niimbot_cache_manager

#endif  // FLUTTER_PLUGIN_NIIMBOT_CACHE_MANAGER_PLUGIN_H_
