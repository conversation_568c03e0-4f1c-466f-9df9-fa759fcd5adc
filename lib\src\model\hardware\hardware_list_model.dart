import 'dart:io';

class HardwareListModel {
  int? total;
  int? page;
  int? limit;
  List<HardwareModel>? list;

  HardwareListModel({this.total, this.page, this.limit, this.list});

  HardwareListModel.fromJson(Map<String, dynamic> json) {
    total = json['total'];
    page = json['page'];
    limit = json['limit'];
    if (json['list'] != null) {
      list = <HardwareModel>[];
      json['list'].forEach((v) {
        list!.add(new HardwareModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['total'] = this.total;
    data['page'] = this.page;
    data['limit'] = this.limit;
    if (this.list != null) {
      data['list'] = this.list!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HardwareModelList {
  List<HardwareModel>? list;

  HardwareModelList.fromJson(List<dynamic> json) {
    list = <HardwareModel>[];
    for (var v in json) {
      HardwareModel model = Platform.isAndroid
          ? HardwareModel.fromJsonAndroid(Map<String, dynamic>.from(v))
          : HardwareModel.fromJson(Map<String, dynamic>.from(v));
      list!.add(model);
    }
  }

  List<dynamic> toJson() {
    List<dynamic> data = [];
    if (list != null) {
      data = list!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HardwareModel {
  String? id;
  String? seriesId;
  String? seriesName;
  List<int>? codes;
  String? name;
  int? status;
  dynamic interfaceType;
  String? interfaceName;
  dynamic printDirection;
  num? defaultWidth;
  num? defaultHeight;
  num? maxPrintHeight;
  num? maxPrintWidth;
  dynamic printMethodCode;
  dynamic printMethodName;
  dynamic securityAction;
  dynamic solubilitySetType;
  String? modelName;
  List<Consumable>? consumables;
  String? paperType;
  dynamic thumb;
  dynamic displayBootPage;
  List<CompatibleWithApplications>? compatibleWithApplications;
  dynamic isSupportCalibration;
  dynamic isSupportWifi;
  dynamic paccuracyName;
  dynamic paccuracy;
  List<String>? rfidNotSupportVersions;
  dynamic rfidType;
  dynamic solubilitySetDefault;
  dynamic solubilitySetEnd;
  dynamic solubilitySetStart;
  dynamic widthSetStart;
  dynamic widthSetEnd;
  dynamic wifiNotSupportVersions;

  HardwareModel(
      {this.id,
      this.seriesId,
      this.codes,
      this.name,
      this.status,
      this.interfaceType,
      this.interfaceName,
      this.printDirection,
      this.defaultWidth,
      this.defaultHeight,
      this.maxPrintWidth,
      this.maxPrintHeight,
      this.printMethodCode,
      this.printMethodName,
      this.securityAction,
      this.solubilitySetType,
      this.modelName,
      this.consumables,
      this.paperType,
      this.thumb,
      this.displayBootPage,
      this.compatibleWithApplications,
      this.isSupportCalibration,
      this.isSupportWifi,
      this.paccuracyName,
      this.paccuracy,
      this.rfidNotSupportVersions,
      this.rfidType,
      this.solubilitySetDefault,
      this.solubilitySetEnd,
      this.solubilitySetStart,
      this.widthSetStart,
      this.widthSetEnd,
      this.wifiNotSupportVersions});

  HardwareModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    seriesId = json['seriesId'];
    seriesName = json['seriesName'];
    codes = json['codes']?.cast<int>();
    name = json['name'];
    status = (json['status'] is int) ? json['status'] : 0;
    interfaceType = json['interfaceType'];
    interfaceName = json['interfaceName'];
    printDirection = json['printDirection'];
    defaultWidth = (json['defaultWidth'] is num) ? json['defaultWidth'] : num.parse(json['defaultWidth']);
    defaultHeight = (json['defaultHeigth'] is num) ? json['defaultHeigth'] : num.parse(json['defaultHeigth']);
    maxPrintWidth = json['maxPrintWidth'];
    maxPrintHeight = json['maxPrintHeight'];
    printMethodCode = json['printMethodCode'];
    printMethodName = json['printMethodName'];
    securityAction = json['securityAction'];
    solubilitySetType = json['solubilitySetType'];
    modelName = json['modelName'];
    if (json['consumables'] != null) {
      consumables = <Consumable>[];
      json['consumables'].forEach((v) {
        consumables!.add(new Consumable.fromJson(v));
      });
    }
    paperType = json['paperType'];
    thumb = json['thumb'];
    displayBootPage = json['displayBootPage'];
    if (json['compatibleWithApplications'] != null) {
      compatibleWithApplications = <CompatibleWithApplications>[];
      json['compatibleWithApplications'].forEach((v) {
        compatibleWithApplications!.add(CompatibleWithApplications.fromJson(v));
      });
    }
    isSupportCalibration = json['isSupportCalibration'];
    isSupportWifi = json['isSupportWifi'];
    paccuracyName = json['paccuracyName'];
    paccuracy = json['paccuracy'];
    rfidNotSupportVersions = json['rfidNotSupportVersions'].cast<String>();
    rfidType = json['rfidType'];
    solubilitySetDefault = json['solubilitySetDefault'];
    solubilitySetEnd = json['solubilitySetEnd'];
    solubilitySetStart = json['solubilitySetStart'];
    widthSetStart = json['widthSetStart'];
    widthSetEnd = json['widthSetEnd'];
    wifiNotSupportVersions = json['wifiNotSupportVersions'];
  }

  HardwareModel.fromJsonAndroid(Map<String, dynamic> json) {
    if (json['id'] is String) {
      id = json['id'];
    } else {
      id = json['id'].toInt().toString();
    }
    seriesId = json['seriesId'];
    seriesName = json['seriesName'];
    codes = json['codes']?.cast<int>();
    name = json['name'];
    status = (json['status'] is int) ? json['status'] : 0;
    interfaceType = json['interfaceType'];
    interfaceName = json['interfaceName'];
    printDirection = json['printDirection'];
    defaultWidth = (json['defaultWidth'] is num) ? json['defaultWidth'] : num.parse(json['defaultWidth']);
    defaultHeight = (json['defaultHeigth'] is num) ? json['defaultHeigth'] : num.parse(json['defaultHeigth']);
    maxPrintWidth = json['maxPrintWidth'];
    maxPrintHeight = json['maxPrintHeight'];
    printMethodCode = json['printMethodCode'];
    printMethodName = json['printMethodName'];
    securityAction = json['securityAction'];
    solubilitySetType = json['solubilitySetType'];
    modelName = json['modelName'];
    if (json['consumables'] != null) {
      consumables = <Consumable>[];
      json['consumables'].forEach((v) {
        consumables!.add(new Consumable.fromJson(Map<String, dynamic>.from(v)));
      });
    }
    paperType = json['paperType'];
    thumb = json['thumb'];
    displayBootPage = json['displayBootPage'];
    if (json['compatibleWithApplications'] != null) {
      compatibleWithApplications = <CompatibleWithApplications>[];
      json['compatibleWithApplications'].forEach((v) {
        compatibleWithApplications!.add(CompatibleWithApplications.fromJson(v));
      });
    }
    isSupportCalibration = json['isSupportCalibration'];
    isSupportWifi = json['isSupportWifi'];
    paccuracyName = json['paccuracyName'];
    paccuracy = json['paccuracy'];
    rfidNotSupportVersions = json['rfidNotSupportVersions'].cast<String>();
    rfidType = json['rfidType'];
    solubilitySetDefault = json['solubilitySetDefault'];
    solubilitySetEnd = json['solubilitySetEnd'];
    solubilitySetStart = json['solubilitySetStart'];
    widthSetStart = json['widthSetStart'];
    widthSetEnd = json['widthSetEnd'];
    wifiNotSupportVersions = json['wifiNotSupportVersions'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['seriesId'] = this.seriesId;
    data['seriesName'] = this.seriesName;
    data['codes'] = this.codes;
    data['name'] = this.name;
    data['status'] = this.status;
    data['interfaceType'] = this.interfaceType;
    data['interfaceName'] = this.interfaceName;
    data['printDirection'] = this.printDirection;
    data['defaultWidth'] = this.defaultWidth;
    data['defaultHeigth'] = this.defaultHeight;
    data['maxPrintWidth'] = this.maxPrintWidth;
    data['maxPrintHeight'] = this.maxPrintHeight;
    data['printMethodCode'] = this.printMethodCode;
    data['printMethodName'] = this.printMethodName;
    data['securityAction'] = this.securityAction;
    data['solubilitySetType'] = this.solubilitySetType;
    data['modelName'] = this.modelName;
    if (this.consumables != null) {
      data['consumables'] = this.consumables!.map((v) => v.toJson()).toList();
    }
    data['paperType'] = this.paperType;
    data['thumb'] = this.thumb;
    data['displayBootPage'] = this.displayBootPage;
    if (this.compatibleWithApplications != null) {
      data['compatibleWithApplications'] = this.compatibleWithApplications!.map((v) => v.toJson()).toList();
    }
    data['isSupportCalibration'] = this.isSupportCalibration;
    data['isSupportWifi'] = this.isSupportWifi;
    data['paccuracyName'] = this.paccuracyName;
    data['paccuracy'] = this.paccuracy;
    data['rfidNotSupportVersions'] = this.rfidNotSupportVersions;
    data['rfidType'] = this.rfidType;
    data['solubilitySetDefault'] = this.solubilitySetDefault;
    data['solubilitySetEnd'] = this.solubilitySetEnd;
    data['solubilitySetStart'] = this.solubilitySetStart;
    data['widthSetStart'] = this.widthSetStart;
    data['widthSetEnd'] = this.widthSetEnd;
    data['wifiNotSupportVersions'] = this.wifiNotSupportVersions;
    return data;
  }

  bool isSupportRecordRfid(){
    return id == '38' && name == 'B32R';
  }
}

class Consumable {
  ParentProperty? parentProperty;
  List<ChildProperties>? childProperties;

  Consumable({this.parentProperty, this.childProperties});

  Consumable.fromJson(Map<String, dynamic> json) {
    parentProperty = json['parentProperty'] != null
        ? new ParentProperty.fromJson(Map<String, dynamic>.from(json['parentProperty']))
        : null;
    if (json['childProperties'] != null) {
      childProperties = <ChildProperties>[];
      json['childProperties'].forEach((v) {
        childProperties!.add(new ChildProperties.fromJson(Map<String, dynamic>.from(v)));
      });
      parentProperty?.childProperties = childProperties;
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.parentProperty != null) {
      data['parentProperty'] = this.parentProperty!.toJson();
    }
    if (this.childProperties != null) {
      data['childProperties'] = this.childProperties!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ParentProperty {
  String? name;
  dynamic? density;
  String? multilingualCode;
  dynamic? code;
  String? printModeValue;
  String? printModeName;
  List<ChildProperties>? childProperties;

  ParentProperty({this.name, this.density, this.multilingualCode, this.code, this.printModeValue, this.printModeName});

  ParentProperty.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    density = json['density'] == null ? 0 : (json['density']).toInt();
    multilingualCode = json['multilingualCode'];
    code = json['code'];
    printModeValue = json['printModeValue'].toString();
    printModeName = json['printModeName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['density'] = this.density;
    data['multilingualCode'] = this.multilingualCode;
    data['code'] = this.code;
    data['printModeValue'] = this.printModeValue;
    data['printModeName'] = this.printModeName;
    return data;
  }
}

class ChildProperties {
  String? name;
  dynamic? code;
  String? multilingualCode;
  String? blindZone;

  ChildProperties({this.name, this.code, this.multilingualCode, this.blindZone});

  ChildProperties.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    code = json['code'];
    multilingualCode = json['multilingualCode'].toString();
    blindZone = json['blindZone'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['code'] = this.code;
    data['multilingualCode'] = this.multilingualCode;
    data['blindZone'] = this.blindZone;
    return data;
  }
}

class CompatibleWithApplications {
  String? code;
  String? name;

  CompatibleWithApplications({this.code, this.name});

  CompatibleWithApplications.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['name'] = this.name;
    return data;
  }
}
