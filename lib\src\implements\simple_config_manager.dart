import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:niimbot_cache_manager/src/define/define.dart';
import 'package:niimbot_cache_manager/src/protocol/simple_config_manager_protocol.dart';
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/services/niimbot_oss_service.dart';
import 'package:niimbot_http/core/niimbot_http_request_options.dart';
import 'package:niimbot_http/core/niimbot_http_response.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';

import '../networkApi/network_api.dart';

class SimpleConfigManager implements SimpleConfigManagerProtocol {
  /// 离线缓存 config
  static const String appConfig = 'offline_appConfig';
  static Map<String, dynamic>? appConfigCache;

  @override
  Future<Map?> getAppConfigInfo({String? defaultConfigInfo}) async {
    try {
      // 检查缓存
      if (appConfigCache != null) {
        return appConfigCache;
      }

      // 获取本地存储的配置
      String? spCache = NiimbotSp().getString(appConfig);

      // 处理默认配置
      if (spCache.isEmpty && defaultConfigInfo?.isNotEmpty == true) {
        spCache = defaultConfigInfo;
      }

      // 尝试获取网络配置
      try {
        final netResult = await _fetchNetworkConfig();
        if (netResult?.data != null) {
          final saveString = json.encode(netResult?.data);
          await NiimbotSp().setString(appConfig, saveString);

          appConfigCache = netResult?.data as Map<String, dynamic>?;
          return appConfigCache;
        }
      } catch (e, s) {
        debugPrint('网络请求异常: $e\n调用栈: $s');
      }

      // 使用本地缓存
      return json.decode(spCache!);
    } catch (e, s) {
      debugPrint('getAppConfigInfo 执行失败:');
      debugPrint('错误详情: $e');
      debugPrint('调用栈: $s');
      return null;
    }
  }

// 新增辅助方法
  Future<NiimbotHttpResponse<dynamic>?> _fetchNetworkConfig() {
    return NiimbotHttpManager.getDefault().getService<NiimbotOSSService>().client.get<dynamic>(ConfigApi.appConfig.path,
        options: NiimbotHttpRequestOptions(
            baseUrl: ConfigApi.appConfig.baseUrl,
            sendTimeout: const Duration(seconds: 2),
            receiveTimeout: const Duration(seconds: 2)));
  }
}
