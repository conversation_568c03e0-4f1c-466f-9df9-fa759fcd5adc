import 'dart:async';

class NiimbotCacheManagerStream {
  static NiimbotCacheManagerStream? _instance;
  StreamController? _streamController;

  // 订阅者集合, <key:list<StreamSubscription>> <订阅者实体对象:StreamSubscription 列表>
  Map<dynamic, List<StreamSubscription>>? subscriptionsMap;

  factory NiimbotCacheManagerStream({bool sync = false}) {
    return _instance ??= NiimbotCacheManagerStream._internal(sync);
  }

  NiimbotCacheManagerStream._internal(bool sync) {
    _streamController = StreamController.broadcast(sync: sync);
    subscriptionsMap = {};
  }

  StreamSubscription register<T>(dynamic subscriber, void Function(dynamic event) onData) {
    StreamSubscription? subscription;

    if (T == dynamic) {
      subscription = _streamController?.stream.listen(onData);
    } else {
      Stream<T>? stream = _streamController?.stream.where((type) => type is T).cast<T>();
      subscription = stream?.listen(onData);
    }

    List<StreamSubscription>? list = subscriptionsMap![subscriber];
    if (list == null) {
      list = [];
      subscriptionsMap![subscriber] = list;
    }
    list.add(subscription!);

    return subscription;
  }

  void post(event) {
    _streamController?.add(event);
  }

  void unregister(dynamic subscriber) {
    List<StreamSubscription>? list = subscriptionsMap![subscriber];
    if (list != null) {
      for (StreamSubscription subscription in list) {
        subscription.cancel();
      }
      subscriptionsMap?.remove(subscriber);
    }
  }
}
