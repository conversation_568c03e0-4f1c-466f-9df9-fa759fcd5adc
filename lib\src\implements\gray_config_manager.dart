import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:niimbot_cache_manager/src/define/define.dart';
import 'package:niimbot_cache_manager/src/model/gray/gray_config.dart';
import 'package:niimbot_cache_manager/src/model/gray/gray_define.dart';
import 'package:niimbot_cache_manager/src/networkApi/network_api.dart';
import 'package:niimbot_cache_manager/src/networkApi/network_listen.dart';
import 'package:niimbot_cache_manager/src/protocol/gray_config_manager_protocol.dart';
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/services/niimbot_rest_service.dart';
import 'package:niimbot_http/core/niimbot_http_request_options.dart';
import 'package:niimbot_http/core/niimbot_http_response.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';

class GrayConfigManager implements GrayConfigManagerProtocol {
  @override
  Future<T?> loadLocalGrayConfig<T>({required String uid, ValueTransformer<T>? transformer}) async {
    String saveKey = GrayDefine.grayConfigKey(uid);
    String? grayConfigJson = NiimbotSp().getString(saveKey);
    if (grayConfigJson?.isNotEmpty == true) {
      try {
        dynamic rawGrayConfig = json.decode(grayConfigJson!);
        if (transformer == null && T is GrayConfig) {
          transformer ??= GrayDefine.grayConfigTransformer as ValueTransformer<T>?;
        }
        T? result = transformer?.call(rawGrayConfig);
        return result;
      } catch (e, s) {
        debugPrint('Exception details:\n $e');
        debugPrint('Stack trace:\n $s');
      }
    }
    return null;
  }

  @override
  Future<Map<String, dynamic>> requestGrayConfigFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer}) async {
    bool isNetworkConnect = await NetWorkListener.checkNetworkConnected();
    if (!isNetworkConnect) {
      return {"result": false, "errorCode": -1, "errorMsg": "网络异常"};
    }
    try {
      NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
          .getService<NiimbotRestService>()
          .client
          .post<dynamic>(GrayApi.grayConfig.path,
              options: NiimbotHttpRequestOptions(baseUrl: GrayApi.grayConfig.baseUrl));
      int? statusCode = netResult.statusCode;
      dynamic rawData = netResult.data?["data"];
      if ((statusCode == 200 || statusCode == 0 || statusCode == 1) && rawData != null) {
        if (transformer == null && T is GrayConfig) {
          transformer ??= GrayDefine.grayConfigTransformer as ValueTransformer<T>?;
        }
        T? data = transformer?.call(rawData);
        return {"result": true, "data": data, "errorCode": 0, "errorMsg": ""};
      }
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return {"result": false, "errorCode": -2, "errorMsg": "网络请求失败"};
  }
}
