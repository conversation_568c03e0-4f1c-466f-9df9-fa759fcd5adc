import 'package:niimbot_cache_manager/src/define/define.dart';

/// [FontManagerProtocol] 字体协议
/// 包含下载，获取缓存等
abstract class FontManagerProtocol {
  /// 加载本地字体分类
  Future<T?> loadLocalFontCategory<T>({ValueTransformer<T>? transformer});

  /// 加载本地所有字体列表
  Future<T?> loadLocalAllFontList<T>({ValueTransformer<T>? transformer});

  /// 加载本地用户字体列表
  Future<T?> loadLocalUserFontList<T>({required String userID, ValueTransformer<T>? transformer});

  /// 清除字体缓存
  Future<bool> clearFontCache();

  /// 清除字体列表缓存
  Future<bool> clearFontListCache();

  /// 清除字体分类缓存
  Future<bool> clearFontCategoryCache();

  /// 从网络请求字体分类
  Future<T?> requestFontCategoryFromNet<T>(
      {ValueTransformer<T>? transformer, ValueUpdateTransformer<T>? updateTransformer});

  /// 从网络请求字体列表
  Future<T?> requestFontListFromNet<T>(
      {required Map<String, dynamic> params,
      ValueTransformer<T>? transformer,
      ValueUpdateTransformer<T>? updateTransformer});

  /// 从网络请求最近使用字体列表
  Future<T?> requestUserFontListFromNet<T>(
      {required String userID, ValueTransformer<T>? transformer, ValueUpdateTransformer<T>? updateTransformer});

  /// 更新用户字体
  Future<bool> updateUserFont({required String userID, required String value});
}
