import 'package:niimbot_cache_manager/src/define/define.dart';
import 'package:niimbot_cache_manager/src/model/font/font_category.dart';
import 'package:niimbot_cache_manager/src/model/font/font_item.dart';

class FontDefine {
  static const String KEY_FONT_CATEGORY = "key_font_category";
  static const String KEY_ALL_FONT_LIST = "key_all_font_list1";
  static const String KEY_USER_FONT_LIST = "key_user_font_list";
  static const String LANGUAGE_EN = "en";
  static const String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
  static const String FONT_DEFAULT_KEY = "fontDefault";
  static const String DEFAULT_FONT_CODE = "ZT001";
  static const String DEFAULT_FONT_CODE_ARIAL = "ZT063";
  static const String DEFAULT_FONT_LOCAL_FILE_NAME = "ZT001.ttf";
  static const String DEFAULT_FONT_ARIAL_LOCAL_FILE_NAME = "ZT063.ttf";

  /// 默认转换器
  static ValueTransformer<List<FontCategory>?> fontCategoryTransformer = (dynamic rawValue) {
    List<FontCategory>? result = [];
    if (rawValue is List<Map<String, dynamic>>) {
      for (var e in rawValue) {
        result.add(FontCategory.fromJson(e));
      }
    }
    return result;
  };

  /// 默认转换器
  static ValueTransformer<List<FontItem>?> fontItemTransformer = (dynamic rawValue) {
    List<FontItem>? result = [];
    if (rawValue is List<Map<String, dynamic>>) {
      for (var e in rawValue) {
        result.add(FontItem.fromJson(e));
      }
    }
    return result;
  };
}
