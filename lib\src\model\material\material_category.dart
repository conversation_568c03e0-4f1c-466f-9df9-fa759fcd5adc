/// 素材分类
class MaterialCategory {
  /// 二级分类id（最近分类和vip分类本地构造，id分别为-2和-1)
  int? id;

  /// 二级分类名称
  String? name;

  /// 一级分类id（最近分类和vip分类本地构造，id分别为-2和-1)
  int? parentId;

  /// 是否显示拉新角标
  int? showNewestVersion;

  MaterialCategory({
    this.id,
    this.name,
    this.parentId,
    this.showNewestVersion,
  });

  MaterialCategory.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    name = json["name"];
    parentId = json["parentId"];
    showNewestVersion = json["showNewestVersion"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data["id"] = id;
    data["name"] = name;
    data["parentId"] = parentId;
    data["showNewestVersion"] = showNewestVersion;
    return data;
  }
}
