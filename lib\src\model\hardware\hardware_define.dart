import 'package:niimbot_cache_manager/src/define/define.dart';
import 'package:niimbot_cache_manager/src/model/hardware/hard_ware_serise_model.dart';
import 'package:niimbot_cache_manager/src/model/hardware/hardware_list_model.dart';

class HardWareDefine {
  /// 打印机型号列表缓存
  static const String hardwareList = 'hardwareListNew';

  /// 默认硬件列表转换器
  static ValueTransformer<HardwareModelList?> hardwareModelListTransformer = (dynamic rawValue) {
    HardwareModelList result = HardwareModelList.fromJson(rawValue);
    return result;
  };

  /// 默认硬件系列转换器
  static ValueTransformer<List<HardWareSeriesModel>?> hardWareSeriesModelTransformer = (dynamic rawValue) {
    List<HardWareSeriesModel>? result = [];
    if (rawValue is List<Map<String, dynamic>>) {
      for (var e in rawValue) {
        result.add(HardWareSeriesModel.fromJson(e));
      }
    }
    return result;
  };
}
