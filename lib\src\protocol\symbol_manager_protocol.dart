import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_cache_manager/src/model/material/material_define.dart';
import 'package:tuple/tuple.dart';

/// [SymbolManagerProtocol] 素材缓存协议
/// 包含网络请求，缓存管理等
abstract class SymbolManagerProtocol {
  /// 加载符号分类
  Future<T?> loadLocalSymbolCategory<T>({required String languageCode, ValueTransformer<T>? transformer});

  /// 加载符号某分类下的数据
  Future<T?> loadLocalSymbolListFromCategory<T>(
      {required String categoryId, required ValueTransformer<T>? transformer});

  /// 从网络请求符号分类
  Future<T?> requestSymbolCategoryFromNet<T>({Map<String, dynamic>? params, ValueTransformer<T>? transformer});

  /// 从网络请求符号分类下的符号数据
  Future<T?> requestSymbolListFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer, JsonTransformer<T>? jsonTransformer});

  /// 清空所有的素材缓存
  Future<bool> clearSymbolCache();

  /// 清空所有的素材列表缓存
  Future<bool> clearSymbolListCache();

  /// 清空所有的素材分类缓存
  Future<bool> clearSymbolCategoryCache();
}
