//
//  Generated file. Do not edit.
//

// clang-format off

#include "generated_plugin_registrant.h"

#include <isar_flutter_libs/isar_flutter_libs_plugin.h>
#include <niimbot_cache_manager/niimbot_cache_manager_plugin.h>

void fl_register_plugins(FlPluginRegistry* registry) {
  g_autoptr(FlPluginRegistrar) isar_flutter_libs_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "IsarFlutterLibsPlugin");
  isar_flutter_libs_plugin_register_with_registrar(isar_flutter_libs_registrar);
  g_autoptr(FlPluginRegistrar) niimbot_cache_manager_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "NiimbotCacheManagerPlugin");
  niimbot_cache_manager_plugin_register_with_registrar(niimbot_cache_manager_registrar);
}
