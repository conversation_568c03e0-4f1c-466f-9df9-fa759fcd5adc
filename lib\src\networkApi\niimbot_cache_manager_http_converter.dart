import 'dart:async';
import 'dart:convert';

import 'package:niimbot_http/core/annotation/json/niimbot_json_mapper.dart';
import 'package:niimbot_http/core/niimbot_http_converter.dart';
import 'package:niimbot_http/core/niimbot_http_request.dart';
import 'package:niimbot_http/core/niimbot_http_response.dart';

class NiimbotDioDefaultHttpConverter extends NiimbotHttpConverter {
  @override
  FutureOr<NiimbotHttpRequest> convertRequest(NiimbotHttpRequest request) async {
    return request;
  }

  @override
  FutureOr<NiimbotHttpResponse<T>> convertResponse<T>(NiimbotHttpResponse response) {
    if (response.data is Map<String, dynamic>) {
      if (T is String) {
        return response.copyWith(body: jsonEncode(response.data) as T?);
      } else if (T == dynamic) {
        return response as NiimbotHttpResponse<T>;
      } else {
        return response.copyWith(body: NiimbotJsonMapper.deserialize<T>(response.data));
      }
    } else {
      response.data = response.data as T;
    }
    return response as NiimbotHttpResponse<T>;
  }
}
