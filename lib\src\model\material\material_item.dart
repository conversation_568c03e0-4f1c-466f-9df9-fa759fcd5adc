///素材详情
class MaterialItem {
  /// 素材id
  int? id;

  /// 素材图片下载地址
  String? image;

  /// 素材图片下载地址
  String? thumbnail;

  /// 素材细分行业id
  int? materialCategoryId;

  /// 素材一级行业id
  int? materialIndustryId;

  /// 素材名称
  String? name;

  /// 使用次数
  int? use;

  /// 使用人数
  int? user;

  /// 是否vip素材
  bool? vip;

  MaterialItem(
      {this.id,
        this.image,
        this.materialCategoryId,
         this.materialIndustryId,
        this.name,
        this.use,
        this.user,
        this.vip});

  MaterialItem.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    image = json["image"];
    thumbnail = json["image"];
    materialCategoryId = json["materialCategoryId"];
    materialIndustryId = json["materialIndustryId"];
    name = json["name"];
    use = json["use"];
    user = json["user"];
    if (json.containsKey("vip")) {
      vip = json["vip"];
    } else {
      vip = false;
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data["id"] = id;
    data["image"] = image;
    data["materialCategoryId"] = materialCategoryId;
    data["materialIndustryId"] = materialIndustryId;
    data["name"] = name;
    data["use"] = use;
    data["user"] = user;
    data["vip"] = vip;
    return data;
  }
}
