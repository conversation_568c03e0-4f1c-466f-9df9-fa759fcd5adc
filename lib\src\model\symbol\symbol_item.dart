// To parse this JSON data, do
//
//     final symbolItem = symbolItemFromJson(jsonString);

import 'dart:convert';

// To parse this JSON data, do
//
//     final symbolItem = symbolItemFromJson(jsonString);

import 'dart:convert';

SymbolItem symbolItemFromJson(String str) => SymbolItem.fromJson(json.decode(str));

String symbolItemToJson(SymbolItem data) => json.encode(data.toJson());

/// 符号元素
class SymbolItem {
  final String? id;
  final String? categoryId;
  final bool? enabled;
  final String? name;
  final String? previewImageUrl;
  final int? unicode;
  final bool? vip;

  SymbolItem({
    this.id,
    this.categoryId,
    this.enabled,
    this.name,
    this.previewImageUrl,
    this.unicode,
    this.vip,
  });

  factory SymbolItem.fromJson(Map<String, dynamic> json) => SymbolItem(
        id: json["id"],
        categoryId: json["categoryId"],
        enabled: json["enabled"],
        name: json["name"],
        previewImageUrl: json["previewImageUrl"],
        unicode: json["unicode"],
        vip: json["vip"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "categoryId": categoryId,
        "enabled": enabled,
        "name": name,
        "previewImageUrl": previewImageUrl,
        "unicode": unicode,
        "vip": vip,
      };
}
