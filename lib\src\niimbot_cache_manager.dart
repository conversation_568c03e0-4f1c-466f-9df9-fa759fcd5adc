import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_cache_manager/src/implements/gray_config_manager.dart';
import 'package:niimbot_cache_manager/src/implements/hardware_manager.dart';
import 'package:niimbot_cache_manager/src/implements/material_manager.dart';
import 'package:niimbot_cache_manager/src/implements/simple_config_manager.dart';
import 'package:niimbot_cache_manager/src/implements/symbol_manager.dart';
import 'package:niimbot_cache_manager/src/model/material/material_define.dart';
import 'package:niimbot_cache_manager/src/networkApi/network_listen.dart';
import 'package:niimbot_cache_manager/src/protocol/font_manager_protocol.dart';
import 'package:niimbot_cache_manager/src/protocol/gray_config_manager_protocol.dart';
import 'package:niimbot_cache_manager/src/protocol/hardware_manager_protocol.dart';
import 'package:niimbot_cache_manager/src/protocol/material_manager_protocol.dart';
import 'package:niimbot_cache_manager/src/protocol/simple_config_manager_protocol.dart';
import 'package:niimbot_cache_manager/src/protocol/symbol_manager_protocol.dart';
import 'package:niimbot_http/business/environment/niimbot_http_env.dart';
import 'package:niimbot_http/business/services/niimbot_graphql_service.dart';
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/services/niimbot_rest_service.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';
import 'package:tuple/tuple.dart';

import 'networkApi/network_api.dart';
import 'package:niimbot_cache_manager/src/implements/consumable_manager.dart';
import 'package:niimbot_cache_manager/src/protocol/consumable_manager_protocol.dart';
import 'package:niimbot_cache_manager/src/model/consumable/consumable_model.dart';

class NiimbotCacheManager
    implements
        FontManagerProtocol,
        HardWareManagerProtocol,
        MaterialManagerProtocol,
        GrayConfigManagerProtocol,
        SymbolManagerProtocol,
        SimpleConfigManagerProtocol,
        ConsumableManagerProtocol {
  /// 工厂构建
  factory NiimbotCacheManager() => _instance;

  /// 单例
  static final NiimbotCacheManager _instance = NiimbotCacheManager._internal(appEnv: AppEnvironment.test);

  /// 内部构造器
  NiimbotCacheManager._internal({required AppEnvironment appEnv}) {
    // 初始化SP
    NiimbotSp().getInstance();
    // 初始化Http
    NiimbotHttpManager.getDefault().updateAttributes(NiimbotHttpGlobalAttributes(
        authCode: '',
        shopToken: '',
        userAgent:
            "AppId/com.suofang.jcbqdy OS/ios AppVersionName/6.1.5 Model/iPhone-11 SystemVersion/14.6 DeviceId/EA400739-C714-45B2-9A73-100B8AAEBFC1 referer/CP001Mobile",
        languageCode: "zh"));
    // 赋予环境
    NetworkApi.appEnv = appEnv;
    // 网络监听
    NetWorkListener.addNetworkConnectivityListener();
    // 事件监听
    triggerListen();
  }

  /// 同步环境以及环境变更
  syncAppEnvWithAttributes(
      {required AppEnvironment appEnv, String? languageCode, String? userAgent, String? authCode, String? shopToken}) {
    _syncAppEnv(appEnv: appEnv);
    _updateAttributes(languageCode: languageCode, userAgent: userAgent, authCode: authCode, shopToken: shopToken);
  }

  /// 同步环境
  _syncAppEnv({required AppEnvironment appEnv}) {
    // 赋予环境
    NetworkApi.appEnv = appEnv;
    NiimbotHttpManager.getDefault()
        .switchApiEnv(appEnv == AppEnvironment.test ? NiimbotHttpApiEnv.dev : NiimbotHttpApiEnv.pro);
  }

  /// 更新环境变更
  _updateAttributes({String? languageCode, String? userAgent, String? authCode, String? shopToken}) {
    NiimbotHttpManager.getDefault().updateAttributes(NiimbotHttpGlobalAttributes(
        authCode: authCode, shopToken: shopToken, userAgent: userAgent, languageCode: languageCode));
    if (authCode?.isEmpty ?? true) {
      NiimbotHttpManager.getDefault().getService<NiimbotRestService>().client.config.headers.remove("Authorization");
      NiimbotHttpManager.getDefault().getService<NiimbotGraphQLService>().client.config.headers.remove("Authorization");
    }
  }

  @override
  Future<T?> loadLocalAllFontList<T>({ValueTransformer<T>? transformer}) {
    return FontManager().loadLocalAllFontList<T>(transformer: transformer);
  }

  @override
  Future<T?> loadLocalFontCategory<T>({ValueTransformer<T>? transformer}) {
    return FontManager().loadLocalFontCategory<T>(transformer: transformer);
  }

  @override
  Future<T?> loadLocalUserFontList<T>({required String userID, ValueTransformer<T>? transformer}) {
    return FontManager().loadLocalUserFontList<T>(userID: userID, transformer: transformer);
  }

  @override
  Future<T?> requestFontCategoryFromNet<T>(
      {ValueTransformer<T>? transformer, ValueUpdateTransformer<T>? updateTransformer}) {
    return FontManager().requestFontCategoryFromNet<T>(transformer: transformer, updateTransformer: updateTransformer);
  }

  @override
  Future<T?> requestFontListFromNet<T>(
      {required Map<String, dynamic> params,
      ValueTransformer<T>? transformer,
      ValueUpdateTransformer<T>? updateTransformer}) {
    return FontManager()
        .requestFontListFromNet<T>(params: params, transformer: transformer, updateTransformer: updateTransformer);
  }

  @override
  Future<T?> requestUserFontListFromNet<T>(
      {required String userID, ValueTransformer<T>? transformer, ValueUpdateTransformer<T>? updateTransformer}) {
    return FontManager()
        .requestUserFontListFromNet<T>(userID: userID, transformer: transformer, updateTransformer: updateTransformer);
  }

  @override
  Future<bool> clearFontCache() {
    return FontManager().clearFontCache();
  }

  @override
  Future<bool> clearFontCategoryCache() {
    return FontManager().clearFontCategoryCache();
  }

  @override
  Future<bool> clearFontListCache() {
    return FontManager().clearFontListCache();
  }

  @override
  Future<bool> updateUserFont({required String userID, required String value}) {
    return FontManager().updateUserFont(userID: userID, value: value);
  }

  @override
  Future<T?> getPrinterInfo<T>({
    ValueTransformer<T>? transformer,
    ValueUpdateTransformer<T>? updateTransformer,
    String? defaultPrinterInfo,
    Function(List<Map<String, dynamic>>?, String result)? requestResultCall,
  }) {
    return HardWareManager().getPrinterInfo(
        transformer: transformer,
        updateTransformer: updateTransformer,
        defaultPrinterInfo: defaultPrinterInfo,
        requestResultCall: requestResultCall);
  }

  @override
  Future<T?> getPrinterSeriesInfo<T>({ValueTransformer<T>? transformer}) {
    return HardWareManager().getPrinterSeriesInfo(transformer: transformer);
  }

  @override
  Future<bool> clearMaterialCache() {
    return MaterialManager().clearMaterialCache();
  }

  @override
  Future<bool> clearMaterialCategoryCache() {
    return MaterialManager().clearMaterialCategoryCache();
  }

  @override
  Future<bool> clearMaterialListCache() {
    return MaterialManager().clearMaterialListCache();
  }

  @override
  Future<List<int>> getBorderVipIdList<T>(
      {bool requestFromNet = false,
      Map<String, dynamic>? extra,
      RefreshGroupListVipStatus? refreshGroupListVipStatus}) {
    return MaterialManager().getBorderVipIdList(
        requestFromNet: requestFromNet, extra: extra, refreshGroupListVipStatus: refreshGroupListVipStatus);
  }

  @override
  Future<List<int>> getIconVipIdList<T>(
      {bool requestFromNet = false,
      Map<String, dynamic>? extra,
      RefreshGroupListVipStatus? refreshGroupListVipStatus}) {
    return MaterialManager().getIconVipIdList(
        requestFromNet: requestFromNet, extra: extra, refreshGroupListVipStatus: refreshGroupListVipStatus);
  }

  @override
  Future<List<T>> loadLocalAllBorderListGroup<T>(
      {required List<Tuple2<int, int>> industryCategoryIdList, required ValueTransformer<T> transformer}) {
    return MaterialManager()
        .loadLocalAllBorderListGroup(industryCategoryIdList: industryCategoryIdList, transformer: transformer);
  }

  @override
  Future<List<T>> loadLocalAllIconListGroup<T>(
      {required List<Tuple2<int, int>> industryCategoryIdList, required ValueTransformer<T> transformer}) {
    return MaterialManager()
        .loadLocalAllIconListGroup(industryCategoryIdList: industryCategoryIdList, transformer: transformer);
  }

  @override
  Future<T?> loadLocalBorderCategory<T>({required String languageCode, ValueTransformer<T>? transformer}) {
    return MaterialManager().loadLocalBorderCategory(languageCode: languageCode, transformer: transformer);
  }

  @override
  Future<T?> loadLocalIconCategory<T>({required String languageCode, ValueTransformer<T>? transformer}) {
    return MaterialManager().loadLocalIconCategory(languageCode: languageCode, transformer: transformer);
  }

  @override
  Future<bool> saveBorderGroup<T>(
      {required T borderGroup, required JsonTransformer<T> jsonTransformer, Map<String, dynamic>? extra}) {
    return MaterialManager().saveBorderGroup(borderGroup: borderGroup, jsonTransformer: jsonTransformer, extra: extra);
  }

  @override
  Future<bool> saveIconGroup<T>(
      {required T iconGroup, required JsonTransformer jsonTransformer, Map<String, dynamic>? extra}) {
    return MaterialManager().saveIconGroup(iconGroup: iconGroup, jsonTransformer: jsonTransformer, extra: extra);
  }

  @override
  Future<Map<String, dynamic>> requestBorderCategoryFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer}) {
    return MaterialManager().requestBorderCategoryFromNet(params: params, transformer: transformer);
  }

  @override
  Future<Map<String, dynamic>> requestBorderListFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer, JsonTransformer<T>? jsonTransformer}) {
    return MaterialManager()
        .requestBorderListFromNet(params: params, transformer: transformer, jsonTransformer: jsonTransformer);
  }

  @override
  Future<Map<String, dynamic>> requestIconCategoryFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer}) {
    return MaterialManager().requestIconCategoryFromNet(params: params, transformer: transformer);
  }

  @override
  Future<Map<String, dynamic>> requestIconListFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer, JsonTransformer<T>? jsonTransformer}) {
    return MaterialManager()
        .requestIconListFromNet(params: params, transformer: transformer, jsonTransformer: jsonTransformer);
  }

  @override
  T? loadBorderGroup<T>({required int industryId, required int categoryId, required ValueTransformer<T> transformer}) {
    return MaterialManager().loadBorderGroup(industryId: industryId, categoryId: categoryId, transformer: transformer);
  }

  @override
  T? loadIconGroup<T>({required int industryId, required int categoryId, required ValueTransformer<T> transformer}) {
    return MaterialManager().loadIconGroup(industryId: industryId, categoryId: categoryId, transformer: transformer);
  }

  @override
  Future<bool> saveBorderVipIdList({required List<int> vipIdList}) {
    return MaterialManager().saveBorderVipIdList(vipIdList: vipIdList);
  }

  @override
  Future<bool> saveIconVipIdList({required List<int> vipIdList}) {
    return MaterialManager().saveIconVipIdList(vipIdList: vipIdList);
  }

  @override
  Future<T?> loadLocalGrayConfig<T>({required String uid, ValueTransformer<T>? transformer}) {
    return GrayConfigManager().loadLocalGrayConfig(uid: uid, transformer: transformer);
  }

  @override
  Future<Map<String, dynamic>> requestGrayConfigFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer}) {
    return GrayConfigManager().requestGrayConfigFromNet(params: params, transformer: transformer);
  }

  @override
  Future<bool> clearSymbolCache() {
    return SymbolManager().clearSymbolCache();
  }

  @override
  Future<bool> clearSymbolCategoryCache() {
    return SymbolManager().clearSymbolCategoryCache();
  }

  @override
  Future<bool> clearSymbolListCache() {
    return SymbolManager().clearSymbolListCache();
  }

  @override
  Future<T?> loadLocalSymbolListFromCategory<T>(
      {required String categoryId, required ValueTransformer<T>? transformer}) {
    return SymbolManager().loadLocalSymbolListFromCategory(categoryId: categoryId, transformer: transformer);
  }

  @override
  Future<T?> loadLocalSymbolCategory<T>({required String languageCode, ValueTransformer<T>? transformer}) {
    return SymbolManager().loadLocalSymbolCategory(languageCode: languageCode, transformer: transformer);
  }

  @override
  Future<T?> requestSymbolCategoryFromNet<T>({Map<String, dynamic>? params, ValueTransformer<T>? transformer}) {
    return SymbolManager().requestSymbolCategoryFromNet(params: params, transformer: transformer);
  }

  @override
  Future<T?> requestSymbolListFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer, JsonTransformer<T>? jsonTransformer}) {
    return SymbolManager().requestSymbolListFromNet(params: params, transformer: transformer);
  }

  @override
  Future<Map?> getAppConfigInfo({String? defaultConfigInfo}) {
    return SimpleConfigManager().getAppConfigInfo(defaultConfigInfo: defaultConfigInfo);
  }

  @override
  Future<bool> clearConsumableCache() {
    return ConsumableManager().clearConsumableCache();
  }

  @override
  Future<T?> loadLocalConsumableSpecs<T>({
    required String consumableCode,
    ValueTransformer<T>? transformer,
  }) {
    return ConsumableManager().loadLocalConsumableSpecs(
      consumableCode: consumableCode,
      transformer: transformer,
    );
  }

  @override
  Future<T?> requestConsumableSpecsFromNet<T>({
    required String consumableCode,
    ValueTransformer<T>? transformer,
    JsonTransformer<T>? jsonTransformer,
  }) {
    return ConsumableManager().requestConsumableSpecsFromNet(
      consumableCode: consumableCode,
      transformer: transformer,
      jsonTransformer: jsonTransformer,
    );
  }

  @override
  Future<bool> saveConsumableSpecs<T>({
    required String consumableCode,
    required T specs,
    required JsonTransformer<T> jsonTransformer,
  }) {
    return ConsumableManager().saveConsumableSpecs(
      consumableCode: consumableCode,
      specs: specs,
      jsonTransformer: jsonTransformer,
    );
  }

  @override
  Future<bool> saveConsumableDataFromJson({required String consumableCode, required String jsonData}) {
    // TODO: implement saveConsumableDataFromJson
    throw UnimplementedError();
  }

  @override
  Future<Tuple3<ConsumableCategory?, ConsumableSpec?, bool>> findConsumableSpecById({
    required String consumableCode,
    required String specId,
    bool preferLocalData = false,
  }) {
    return ConsumableManager().findConsumableSpecById(
      consumableCode: consumableCode,
      specId: specId,
      preferLocalData: preferLocalData,
    );
  }
}

extension Listen on NiimbotCacheManager {
  /// 触发监听，例如触发App启动、切换语言等
  triggerListen() {
    NiimbotCacheManagerStream().register<TriggerTime>(this, (event) {
      switch (event as TriggerTime) {
        case TriggerTime.startApp:
          // TODO: Handle this case.
          break;
        case TriggerTime.languageChange:
          // TODO: Handle this case.
          break;
        case TriggerTime.loginStatusChange:
          // TODO: Handle this case.
          break;
        case TriggerTime.vipChange:
          // TODO: Handle this case.
          break;
      }
    });
  }
}
