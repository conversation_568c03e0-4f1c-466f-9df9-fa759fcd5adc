import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:niimbot_cache_manager/src/define/define.dart';
import 'package:niimbot_cache_manager/src/model/material/material_category.dart';
import 'package:niimbot_cache_manager/src/model/material/material_define.dart';
import 'package:niimbot_cache_manager/src/networkApi/network_api.dart';
import 'package:niimbot_cache_manager/src/networkApi/network_listen.dart';
import 'package:niimbot_cache_manager/src/protocol/material_manager_protocol.dart';
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/services/niimbot_rest_service.dart';
import 'package:niimbot_http/core/niimbot_http_request_options.dart';
import 'package:niimbot_http/core/niimbot_http_response.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';
import 'package:tuple/tuple.dart';

class MaterialManager implements MaterialManagerProtocol {
  /// 工厂构建
  factory MaterialManager() => _instance;

  /// 单例
  static final MaterialManager _instance = MaterialManager._internal();

  /// 内部构造
  MaterialManager._internal() {}

  @override
  Future<bool> clearMaterialCache() async {
    bool fail = false;
    var keys = NiimbotSp().getKeys();
    for (var key in keys) {
      if (key.startsWith(MaterialDefine.Category_List_Key_Prefix) ||
          key.startsWith(MaterialDefine.Material_Item_List_Key_Prefix) ||
          key.startsWith(MaterialDefine.Material_Item_List_Recent_Key) ||
          key.startsWith(MaterialDefine.Material_Item_List_Vip_Key) ||
          key.startsWith(MaterialDefine.Material_Item_Vip_Ids_Key)) {
        bool result = await NiimbotSp().remove(key);
        if (!result) {
          fail = true;
        }
      }
    }
    return !fail;
  }

  @override
  Future<bool> clearMaterialCategoryCache() async {
    bool fail = false;
    var keys = NiimbotSp().getKeys();
    for (var key in keys) {
      if (key.startsWith(MaterialDefine.Category_List_Key_Prefix)) {
        bool result = await NiimbotSp().remove(key);
        if (!result) {
          fail = true;
        }
      }
    }
    return !fail;
  }

  @override
  Future<bool> clearMaterialListCache() async {
    bool fail = false;
    var keys = NiimbotSp().getKeys();
    for (var key in keys) {
      if (key.startsWith(MaterialDefine.Material_Item_List_Key_Prefix) ||
          key.startsWith(MaterialDefine.Material_Item_List_Recent_Key) ||
          key.startsWith(MaterialDefine.Material_Item_List_Vip_Key)) {
        bool result = await NiimbotSp().remove(key);
        if (!result) {
          fail = true;
        }
      }
    }
    return !fail;
  }

  @override
  Future<List<T>> loadLocalAllBorderListGroup<T>(
      {required List<Tuple2<int, int>> industryCategoryIdList, required ValueTransformer<T> transformer}) async {
    List<T> list = [];
    for (var element in industryCategoryIdList) {
      int industryId = element.item1;
      int categoryId = element.item2;
      String saveKey = MaterialDefine.getMaterialListKey(industryId, categoryId, true);
      String? borderGroupJson = NiimbotSp().getString(saveKey);
      if (borderGroupJson?.isNotEmpty == true) {
        try {
          Map<String, dynamic> data = json.decode(borderGroupJson!);
          T? t = transformer.call(data);
          if (t != null) {
            list.add(t);
          }
        } catch (e, s) {
          debugPrint('Exception details:\n $e');
          debugPrint('Stack trace:\n $s');
        }
      }
    }
    return list;
  }

  @override
  Future<List<T>> loadLocalAllIconListGroup<T>(
      {required List<Tuple2<int, int>> industryCategoryIdList, required ValueTransformer<T> transformer}) async {
    List<T> list = [];
    for (var element in industryCategoryIdList) {
      int industryId = element.item1;
      int categoryId = element.item2;
      String saveKey = MaterialDefine.getMaterialListKey(industryId, categoryId, false);
      String? borderGroupJson = NiimbotSp().getString(saveKey);
      if (borderGroupJson?.isNotEmpty == true) {
        try {
          dynamic data = json.decode(borderGroupJson!);
          T? t = transformer.call(data);
          if (t != null) {
            list.add(t);
          }
        } catch (e, s) {
          debugPrint('Exception details:\n $e');
          debugPrint('Stack trace:\n $s');
        }
      }
    }
    return list;
  }

  @override
  Future<T?> loadLocalBorderCategory<T>({required String languageCode, ValueTransformer<T>? transformer}) async {
    String saveKey = MaterialDefine.getMaterialCategoryListKey(languageCode, true);
    String? categoryListJson = NiimbotSp().getString(saveKey);
    if (categoryListJson?.isNotEmpty == true) {
      try {
        dynamic rawBorderCategory = json.decode(categoryListJson!);
        // 自定义转换器
        if (transformer == null && T is List<MaterialCategory>) {
          // 启用默认转换器
          transformer ??= MaterialDefine.materialCategoryTransformer as ValueTransformer<T>?;
        }
        T? result = transformer?.call(rawBorderCategory);
        return result;
      } catch (e, s) {
        debugPrint('Exception details:\n $e');
        debugPrint('Stack trace:\n $s');
      }
    }
    return null;
  }

  @override
  Future<T?> loadLocalIconCategory<T>({required String languageCode, ValueTransformer<T>? transformer}) async {
    String saveKey = MaterialDefine.getMaterialCategoryListKey(languageCode, false);
    String? categoryListJson = NiimbotSp().getString(saveKey);
    if (categoryListJson?.isNotEmpty == true) {
      try {
        dynamic rawBorderCategory = json.decode(categoryListJson!);
        // 自定义转换器
        if (transformer == null && T is List<MaterialCategory>) {
          // 启用默认转换器
          transformer ??= MaterialDefine.materialCategoryTransformer as ValueTransformer<T>?;
        }
        T? result = transformer?.call(rawBorderCategory);
        return result;
      } catch (e, s) {
        debugPrint('Exception details:\n $e');
        debugPrint('Stack trace:\n $s');
      }
    }
    return null;
  }

  @override
  T? loadBorderGroup<T>({required int industryId, required int categoryId, required ValueTransformer<T> transformer}) {
    String saveKey = MaterialDefine.getMaterialListKey(industryId, categoryId, true);
    String? groupJson = NiimbotSp().getString(saveKey);
    if (groupJson?.isNotEmpty == true) {
      return transformer.call(json.decode(groupJson!));
    }
    return null;
  }

  @override
  T? loadIconGroup<T>({required int industryId, required int categoryId, required ValueTransformer<T> transformer}) {
    String saveKey = MaterialDefine.getMaterialListKey(industryId, categoryId, false);
    String? groupJson = NiimbotSp().getString(saveKey);
    if (groupJson?.isNotEmpty == true) {
      return transformer.call(json.decode(groupJson!));
    }
    return null;
  }

  @override
  Future<bool> saveBorderGroup<T>(
      {required T borderGroup, required JsonTransformer<T> jsonTransformer, Map<String, dynamic>? extra}) async {
    int industryId = extra!["industryId"];
    int categoryId = extra!["categoryId"];
    String saveKey = MaterialDefine.getMaterialListKey(industryId, categoryId, true);
    try {
      String? recentUseGroupJson = json.encode(jsonTransformer.call(value: borderGroup));
      return NiimbotSp().setString(saveKey, recentUseGroupJson);
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
      return false;
    }
  }

  @override
  Future<bool> saveIconGroup<T>(
      {required T iconGroup, required JsonTransformer jsonTransformer, Map<String, dynamic>? extra}) async {
    int industryId = extra!["industryId"];
    int categoryId = extra!["categoryId"];
    String saveKey = MaterialDefine.getMaterialListKey(industryId, categoryId, false);
    try {
      String? recentUseGroupJson = json.encode(jsonTransformer.call(value: iconGroup));
      return NiimbotSp().setString(saveKey, recentUseGroupJson);
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
      return false;
    }
  }

  @override
  Future<List<int>> getBorderVipIdList<T>(
      {bool requestFromNet = false,
      Map<String, dynamic>? extra,
      RefreshGroupListVipStatus? refreshGroupListVipStatus}) async {
    List<int> vipIdList = [];
    String saveKey = MaterialDefine.getMaterialVipIdsKey(true);
    String? vipIdListJson = NiimbotSp().getString(saveKey);
    if (vipIdListJson?.isNotEmpty == true) {
      json.decode(vipIdListJson!).forEach((e) {
        vipIdList.add(e);
      });
    }
    bool isNetworkConnect = await NetWorkListener.checkNetworkConnected();
    if (requestFromNet && isNetworkConnect) {
      try {
        NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
            .getService<NiimbotRestService>()
            .client
            .post<dynamic>(MaterialApi.vipBorderIdList.path,
                options: NiimbotHttpRequestOptions(baseUrl: MaterialApi.vipBorderIdList.baseUrl));
        int? statusCode = netResult.statusCode;
        List? data = netResult.data?["data"];
        if ((statusCode == 200 || statusCode == 0 || statusCode == 1) && data?.isNotEmpty == true) {
          vipIdList.clear();
          for (var element in data!) {
            vipIdList.add(element);
          }
          if (vipIdList.isNotEmpty) {
            await NiimbotSp().setString(saveKey, json.encode(vipIdList));
          } else {
            await NiimbotSp().remove(saveKey);
          }
          if (refreshGroupListVipStatus != null) {
            Map<String, dynamic> arguments = extra ?? {};
            arguments["vipIdList"] = vipIdList;
            refreshGroupListVipStatus.call(arguments: arguments);
          }
        }
      } catch (e, s) {
        debugPrint('Exception details:\n $e');
        debugPrint('Stack trace:\n $s');
      }
    }
    return vipIdList;
  }

  @override
  Future<List<int>> getIconVipIdList<T>(
      {bool requestFromNet = false,
      Map<String, dynamic>? extra,
      RefreshGroupListVipStatus? refreshGroupListVipStatus}) async {
    List<int> vipIdList = [];
    String saveKey = MaterialDefine.getMaterialVipIdsKey(false);
    String? vipIdListJson = NiimbotSp().getString(saveKey);
    if (vipIdListJson?.isNotEmpty == true) {
      json.decode(vipIdListJson!).forEach((e) {
        vipIdList.add(e);
      });
    }
    bool isNetworkConnect = await NetWorkListener.checkNetworkConnected();
    if (requestFromNet && isNetworkConnect) {
      try {
        NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
            .getService<NiimbotRestService>()
            .client
            .post<dynamic>(MaterialApi.vipIconIdList.path,
                options: NiimbotHttpRequestOptions(baseUrl: MaterialApi.vipIconIdList.baseUrl));
        int? statusCode = netResult.statusCode;
        List? data = netResult.data?["data"];
        if ((statusCode == 200 || statusCode == 0 || statusCode == 1) && data?.isNotEmpty == true) {
          vipIdList.clear();
          for (var element in data!) {
            vipIdList.add(element);
          }
          if (vipIdList.isNotEmpty) {
            await NiimbotSp().setString(saveKey, json.encode(vipIdList));
          } else {
            await NiimbotSp().remove(saveKey);
          }
          if (refreshGroupListVipStatus != null) {
            Map<String, dynamic> arguments = extra ?? {};
            arguments["vipIdList"] = vipIdList;
            refreshGroupListVipStatus.call(arguments: arguments);
          }
        }
      } catch (e, s) {
        debugPrint('Exception details:\n $e');
        debugPrint('Stack trace:\n $s');
      }
    }
    return vipIdList;
  }

  @override
  Future<Map<String, dynamic>> requestBorderCategoryFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer}) async {
    bool isNetworkConnect = await NetWorkListener.checkNetworkConnected();
    if (!isNetworkConnect) {
      return {"result": false, "errorCode": -1, "errorMsg": "网络异常"};
    }
    try {
      NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
          .getService<NiimbotRestService>()
          .client
          .post<dynamic>(MaterialApi.borderCategoryList.path,
              options: NiimbotHttpRequestOptions(baseUrl: MaterialApi.borderCategoryList.baseUrl));
      int? statusCode = netResult.statusCode;
      dynamic rawData = netResult.data?["data"];
      if ((statusCode == 200 || statusCode == 0 || statusCode == 1) && rawData != null) {
        // 子级提升，二级分类作为展示，一级分类作为数据
        List catData = [];
        for (var element in rawData) {
          if (element.containsKey('cat')) {
            catData.addAll(element['cat']);
          }
        }
        String catDataJson = json.encode(catData);
        if (transformer == null && T is List<MaterialCategory>) {
          transformer ??= MaterialDefine.materialCategoryTransformer as ValueTransformer<T>?;
        }
        T? data = transformer?.call(catData);
        if (params?.containsKey("languageCode") == true) {
          String languageCode = params!["languageCode"] as String;
          String saveKey = MaterialDefine.getMaterialCategoryListKey(languageCode, true);
          await NiimbotSp().setString(saveKey, catDataJson);
        }
        return {"result": true, "data": data, "errorCode": 0, "errorMsg": ""};
      }
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return {"result": false, "errorCode": -2, "errorMsg": "网络请求失败"};
  }

  @override
  Future<Map<String, dynamic>> requestBorderListFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer, JsonTransformer<T>? jsonTransformer}) async {
    bool isNetworkConnect = await NetWorkListener.checkNetworkConnected();
    if (!isNetworkConnect) {
      return {"result": false, "errorCode": -1, "errorMsg": "网络异常"};
    }
    try {
      int borderIndustryId = params!["borderIndustryId"];
      int borderCategoryId = params["borderCategoryId"];
      Map<String, dynamic> requestParams = {};
      requestParams["page"] = params["page"];
      requestParams["limit"] = params["limit"];
      requestParams["vip"] = params["vip"];
      if (borderIndustryId > 0) {
        requestParams["borderIndustryId"] = borderIndustryId;
      }
      if (borderCategoryId > 0) {
        requestParams["borderCategoryId"] = borderCategoryId;
      }
      NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
          .getService<NiimbotRestService>()
          .client
          .post<dynamic>(MaterialApi.borderList.path,
              data: requestParams, options: NiimbotHttpRequestOptions(baseUrl: MaterialApi.borderList.baseUrl));
      int? statusCode = netResult.statusCode;
      dynamic rawData = netResult.data?["data"];
      if ((statusCode == 200 || statusCode == 0 || statusCode == 1) && rawData != null) {
        if (transformer != null && jsonTransformer != null) {
          T? data = transformer.call(rawData["list"]);
          if (data != null) {
            String saveKey = MaterialDefine.getMaterialListKey(borderIndustryId, borderCategoryId, true);
            String groupJson = json.encode(jsonTransformer.call(value: data));
            await NiimbotSp().setString(saveKey, groupJson);
          }
          return {"result": true, "data": data, "errorCode": 0, "errorMsg": ""};
        }
        if (transformer == null && T is List<MaterialCategory>) {
          transformer ??= MaterialDefine.materialItemTransformer as ValueTransformer<T>?;
        }
        T? data = transformer?.call(rawData["list"]);
        return {"result": true, "data": data, "errorCode": 0, "errorMsg": ""};
      }
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return {"result": false, "errorCode": -2, "errorMsg": "网络请求失败"};
  }

  @override
  Future<Map<String, dynamic>> requestIconCategoryFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer}) async {
    bool isNetworkConnect = await NetWorkListener.checkNetworkConnected();
    if (!isNetworkConnect) {
      return {"result": false, "errorCode": -1, "errorMsg": "网络异常"};
    }
    try {
      NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
          .getService<NiimbotRestService>()
          .client
          .post<dynamic>(MaterialApi.iconCategoryList.path,
              options: NiimbotHttpRequestOptions(baseUrl: MaterialApi.iconCategoryList.baseUrl));
      int? statusCode = netResult.statusCode;
      dynamic rawData = netResult.data?["data"];
      if ((statusCode == 200 || statusCode == 0 || statusCode == 1) && rawData != null) {
        // 子级提升，二级分类作为展示，一级分类作为数据
        List catData = [];
        for (var element in rawData) {
          if (element.containsKey('cat')) {
            catData.addAll(element['cat']);
          }
        }
        String catDataJson = json.encode(catData);
        if (transformer == null && T is List<MaterialCategory>) {
          transformer ??= MaterialDefine.materialCategoryTransformer as ValueTransformer<T>?;
        }
        T? data = transformer?.call(catData);
        if (params?.containsKey("languageCode") == true) {
          String languageCode = params!["languageCode"] as String;
          String saveKey = MaterialDefine.getMaterialCategoryListKey(languageCode, false);
          await NiimbotSp().setString(saveKey, catDataJson);
        }
        return {"result": true, "data": data, "errorCode": 0, "errorMsg": ""};
      }
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return {"result": false, "errorCode": -2, "errorMsg": "网络请求失败"};
  }

  @override
  Future<Map<String, dynamic>> requestIconListFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer, JsonTransformer<T>? jsonTransformer}) async {
    bool isNetworkConnect = await NetWorkListener.checkNetworkConnected();
    if (!isNetworkConnect) {
      return {"result": false, "errorCode": -1, "errorMsg": "网络异常"};
    }
    try {
      int materialIndustryId = params!["materialIndustryId"];
      int materialCategoryId = params["materialCategoryId"];
      Map<String, dynamic> requestParams = {};
      requestParams["page"] = params["page"];
      requestParams["limit"] = params["limit"];
      requestParams["vip"] = params["vip"];
      if (materialIndustryId > 0) {
        requestParams["materialIndustryId"] = materialIndustryId;
      }
      if (materialCategoryId > 0) {
        requestParams["materialCategoryId"] = materialCategoryId;
      }
      NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
          .getService<NiimbotRestService>()
          .client
          .post<dynamic>(MaterialApi.iconList.path,
              data: requestParams, options: NiimbotHttpRequestOptions(baseUrl: MaterialApi.iconList.baseUrl));
      int? statusCode = netResult.statusCode;
      dynamic rawData = netResult.data?["data"];
      if ((statusCode == 200 || statusCode == 0 || statusCode == 1) && rawData != null) {
        if (transformer != null && jsonTransformer != null) {
          T? data = transformer.call(rawData["list"]);
          if (data != null) {
            String saveKey = MaterialDefine.getMaterialListKey(materialIndustryId, materialCategoryId, false);
            String groupJson = json.encode(jsonTransformer.call(value: data));
            await NiimbotSp().setString(saveKey, groupJson);
          }
          return {"result": true, "data": data, "errorCode": 0, "errorMsg": ""};
        }
        if (transformer == null && T is List<MaterialCategory>) {
          transformer ??= MaterialDefine.materialItemTransformer as ValueTransformer<T>?;
        }
        T? data = transformer?.call(rawData["list"]);
        return {"result": true, "data": data, "errorCode": 0, "errorMsg": ""};
      }
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return {"result": false, "errorCode": -2, "errorMsg": ""};
  }

  @override
  Future<bool> saveBorderVipIdList({required List<int> vipIdList}) {
    String saveKey = MaterialDefine.getMaterialVipIdsKey(true);
    return NiimbotSp().setString(saveKey, json.encode(vipIdList));
  }

  @override
  Future<bool> saveIconVipIdList({required List<int> vipIdList}) {
    String saveKey = MaterialDefine.getMaterialVipIdsKey(false);
    return NiimbotSp().setString(saveKey, json.encode(vipIdList));
  }
}
