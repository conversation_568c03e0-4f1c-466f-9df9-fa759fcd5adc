import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_cache_manager/src/model/gray/gray_config.dart';

class GrayDefine {
  ///灰度配置缓存
  static const String grayConfigKeySuffix = 'ab_test_gray_config';

  static String grayConfigKey(String uid) {
    return '${uid}_$grayConfigKeySuffix';
  }

  /// 默认灰度配置转换器
  static ValueTransformer<GrayConfig?> grayConfigTransformer =
      (dynamic rawValue) {
    GrayConfig result = GrayConfig.fromJson(rawValue);
    return result;
  };
}
