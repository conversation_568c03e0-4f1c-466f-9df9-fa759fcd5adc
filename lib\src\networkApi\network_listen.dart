import 'package:connectivity_plus/connectivity_plus.dart';

class NetWorkListener {
  static bool isNetworkConnected = true;

  static addNetworkConnectivityListener() {
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      bool networkConnectedNew = result != ConnectivityResult.none;
      if (networkConnectedNew != isNetworkConnected) {
        isNetworkConnected = networkConnectedNew;
      }
    });
  }

  static Future<bool> checkNetworkConnected() async {
    ConnectivityResult result = await Connectivity().checkConnectivity();
    isNetworkConnected = result != ConnectivityResult.none;
    return isNetworkConnected;
  }
}
