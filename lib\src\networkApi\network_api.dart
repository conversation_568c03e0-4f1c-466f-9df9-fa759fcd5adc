import 'package:niimbot_cache_manager/src/define/define.dart';

class ApiModel {
  /// host
  final String baseUrl;

  /// path
  final String path;

  ApiModel({required this.baseUrl, required this.path});
}

class NetworkApi {
  /// app环境
  static AppEnvironment appEnv = AppEnvironment.test;

  /// 获取baseURl
  static String get httpBaseUrl => appEnv == AppEnvironment.test
      ? "https://print.jc-test.cn/api"
      : "https://print.niimbot.com/api";

  /// 获取baseURl
  static String get ossBaseUrl => appEnv == AppEnvironment.test
      ? "https://oss-print-fat.jc-test.cn"
      : "https://oss-print.niimbot.com";
}

extension FontApi on NetworkApi {
  /// 所有字体分类集合
  static ApiModel get fontClassifyList => ApiModel(
      baseUrl: NetworkApi.httpBaseUrl, path: '/content/fontlib/classifies');

  /// 所有字体列表
  static ApiModel get fontList => ApiModel(
      baseUrl: NetworkApi.ossBaseUrl,
      path: '/public_resources/static_resources/font.json');
}

extension HardWareApi on NetworkApi {
  /// 硬件列表
  static ApiModel get hardwareList => ApiModel(
      baseUrl: NetworkApi.ossBaseUrl,
      path: '/public_resources/static_resources/devices.json');

  /// 硬件系列列表
  static ApiModel get deviceSeriesList => ApiModel(
      baseUrl: NetworkApi.httpBaseUrl, path: '/content/guidePage/list');
}

extension MaterialApi on NetworkApi {
  /// vip图标id列表
  static ApiModel get vipIconIdList => ApiModel(
      baseUrl: NetworkApi.httpBaseUrl, path: '/materialLib/get/vip-ids');

  /// vip线框id列表
  static ApiModel get vipBorderIdList => ApiModel(
      baseUrl: NetworkApi.httpBaseUrl, path: '/material-borders/vip-ids');

  /// 图标分类列表
  static ApiModel get iconCategoryList =>
      ApiModel(baseUrl: NetworkApi.httpBaseUrl, path: '/materialIndCat/list');

  /// 线框分类列表
  static ApiModel get borderCategoryList => ApiModel(
      baseUrl: NetworkApi.httpBaseUrl, path: '/material-border-indcat/list');

  /// 图标分类下的图标列表
  static ApiModel get iconList =>
      ApiModel(baseUrl: NetworkApi.httpBaseUrl, path: '/materialLib/page');

  /// 线框分类下的图标列表
  static ApiModel get borderList =>
      ApiModel(baseUrl: NetworkApi.httpBaseUrl, path: '/material-borders/page');
}

extension GrayApi on NetworkApi {
  /// 拉取灰度配置
  static ApiModel get grayConfig => ApiModel(
      baseUrl: NetworkApi.httpBaseUrl, path: '/system/user/stgStrategyConfig');
}

extension SymbolApi on NetworkApi {
  /// 符号分类列表
  static ApiModel get symbolCategoryList =>
      ApiModel(baseUrl: NetworkApi.httpBaseUrl, path: '/symbols/categories');

  /// 符号分类下的符号列表
  static ApiModel get symbolList =>
      ApiModel(baseUrl: NetworkApi.httpBaseUrl, path: '/symbols/page');
}

extension ConfigApi on NetworkApi {
  /// appConfig配置文件
  static ApiModel get appConfig => ApiModel(
      baseUrl: NetworkApi.ossBaseUrl,
      path: '/public_resources/static_resources/app_config.json');

  // /// app内应用列表配
  // static ApiModel get innerAppList => ApiModel(baseUrl: NetworkApi.httpBaseUrl, path: '/symbols/page');
}

extension ConsumableApi on NetworkApi {
  /// Get consumable specifications list
  static ApiModel get consumableSpecList =>
      ApiModel(baseUrl: NetworkApi.httpBaseUrl, path: '/labels/tube/spec-list');
}
