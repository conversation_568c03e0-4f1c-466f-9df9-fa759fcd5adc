import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'niimbot_cache_manager_method_channel.dart';

abstract class NiimbotCacheManagerPlatform extends PlatformInterface {
  /// Constructs a NiimbotCacheManagerPlatform.
  NiimbotCacheManagerPlatform() : super(token: _token);

  static final Object _token = Object();

  static NiimbotCacheManagerPlatform _instance = MethodChannelNiimbotCacheManager();

  /// The default instance of [NiimbotCacheManagerPlatform] to use.
  ///
  /// Defaults to [MethodChannelNiimbotCacheManager].
  static NiimbotCacheManagerPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [NiimbotCacheManagerPlatform] when
  /// they register themselves.
  static set instance(NiimbotCacheManagerPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
