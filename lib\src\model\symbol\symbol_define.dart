import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_cache_manager/src/model/material/material_category.dart';
import 'package:niimbot_cache_manager/src/model/material/material_item.dart';
import 'package:niimbot_cache_manager/src/model/symbol/symbol_category.dart';
import 'package:niimbot_cache_manager/src/model/symbol/symbol_item.dart';

class SymbolDefine {
  static const String Category_List_Key = "symbol_category_list";
  static const String Category_Item_List_Key = "symbol_item_list";

  /// 默认转换器
  static ValueTransformer<List<SymbolCategory>?> symbolCategoryTransformer = (dynamic rawValue) {
    List<SymbolCategory>? result = [];
    if (rawValue is List<Map<String, dynamic>>) {
      for (var e in rawValue) {
        result.add(SymbolCategory.fromJson(e));
      }
    }
    return result;
  };

  /// 默认转换器
  static ValueTransformer<List<SymbolItem>?> symbolItemTransformer = (dynamic rawValue) {
    List<SymbolItem>? result = [];
    if (rawValue is List<Map<String, dynamic>>) {
      for (var e in rawValue) {
        result.add(SymbolItem.fromJson(e));
      }
    }
    return result;
  };

  /// 保存素材分类的key
  static String getSymbolCategoryListKey(String languageCode) {
    String key;
    if (languageCode.isNotEmpty) {
      key = "${Category_List_Key}_${languageCode}";
    } else {
      key = Category_List_Key;
    }
    return key;
  }

  /// 保存素材列表的key
  static String getSymbolListKey(String? categoryId) {
    return "${Category_Item_List_Key}_${categoryId}";
  }
}
