import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:niimbot_cache_manager/src/define/define.dart';
import 'package:niimbot_cache_manager/src/model/material/material_category.dart';
import 'package:niimbot_cache_manager/src/model/material/material_define.dart';
import 'package:niimbot_cache_manager/src/model/symbol/symbol_category.dart';
import 'package:niimbot_cache_manager/src/model/symbol/symbol_define.dart';
import 'package:niimbot_cache_manager/src/networkApi/network_api.dart';
import 'package:niimbot_cache_manager/src/networkApi/network_listen.dart';
import 'package:niimbot_cache_manager/src/protocol/symbol_manager_protocol.dart';
import 'package:niimbot_http/business/services/niimbot_http_manager.dart';
import 'package:niimbot_http/business/services/niimbot_rest_service.dart';
import 'package:niimbot_http/core/niimbot_http_request.dart';
import 'package:niimbot_http/core/niimbot_http_request_options.dart';
import 'package:niimbot_http/core/niimbot_http_response.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';
import 'package:tuple/tuple.dart';

class SymbolManager implements SymbolManagerProtocol {
  /// 工厂构建
  factory SymbolManager() => _instance;

  /// 单例
  static final SymbolManager _instance = SymbolManager._internal();

  /// 内部构造
  SymbolManager._internal();

  @override
  Future<T?> loadLocalSymbolCategory<T>({required String languageCode, ValueTransformer<T>? transformer}) async {
    String saveKey = SymbolDefine.getSymbolCategoryListKey(languageCode);
    String? categoryListJson = NiimbotSp().getString(saveKey);
    if (categoryListJson?.isNotEmpty == true) {
      try {
        dynamic rawCategory = json.decode(categoryListJson!);
        // 自定义转换器
        if (transformer == null && T is List<SymbolCategory>) {
          // 启用默认转换器
          transformer ??= SymbolDefine.symbolCategoryTransformer as ValueTransformer<T>?;
        }
        T? result = transformer?.call(rawCategory);
        return result;
      } catch (e, s) {
        debugPrint('Exception details:\n $e');
        debugPrint('Stack trace:\n $s');
      }
    }
    return null;
  }

  @override
  Future<T?> loadLocalSymbolListFromCategory<T>(
      {required String categoryId, required ValueTransformer<T>? transformer}) async {
    String saveKey = SymbolDefine.getSymbolListKey(categoryId);
    String? categoryListJson = NiimbotSp().getString(saveKey);
    if (categoryListJson.isNotEmpty == true) {
      try {
        dynamic rawCategory = json.decode(categoryListJson);
        // 自定义转换器
        if (transformer == null && T is List<SymbolCategory>) {
          // 启用默认转换器
          transformer ??= SymbolDefine.symbolItemTransformer as ValueTransformer<T>?;
        }
        T? result = transformer?.call(rawCategory);
        return result;
      } catch (e, s) {
        debugPrint('Exception details:\n $e');
        debugPrint('Stack trace:\n $s');
      }
    }
    return null;
  }

  @override
  Future<T?> requestSymbolCategoryFromNet<T>({Map<String, dynamic>? params, ValueTransformer<T>? transformer}) async {
    bool isNetworkConnect = await NetWorkListener.checkNetworkConnected();
    if (!isNetworkConnect) {
      return null;
    }
    try {
      // NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
      //     .getService<NiimbotRestService>()
      //     .client
      //     .get<dynamic>(SymbolApi.symbolCategoryList.path,
      //         options: NiimbotHttpRequestOptions(baseUrl: SymbolApi.symbolCategoryList.baseUrl));
      var request = NiimbotHttpRequest(
          path: SymbolApi.symbolCategoryList.path,
          method: "GET",
          queryParams: params,
          options: NiimbotHttpRequestOptions(
              baseUrl: SymbolApi.symbolCategoryList.baseUrl,
              userAgent: NiimbotHttpManager.getDefault().getService<NiimbotRestService>().client.config.userAgent));
      NiimbotHttpResponse<dynamic> netResult =
          await NiimbotHttpManager.getDefault().getService<NiimbotRestService>().client.request<dynamic>(request);
      int? statusCode = netResult.statusCode;
      dynamic rawData = netResult.data?["data"];
      if ((statusCode == 200 || statusCode == 0 || statusCode == 1) && rawData != null) {
        // 解析response
        List<Map<String, dynamic>>? data =
            rawData?.map<Map<String, dynamic>>((e) => Map<String, dynamic>.from(e)).toList();
        String dataJson = json.encode(data);
        // 自定义转换器
        if (transformer == null && T is List<SymbolCategory>) {
          transformer ??= SymbolDefine.symbolCategoryTransformer as ValueTransformer<T>?;
        }
        // 获取结果
        T? result = transformer?.call(data);
        // 存储结果
        if (params?.containsKey("languageCode") == true) {
          String languageCode = params!["languageCode"] as String;
          String saveKey = SymbolDefine.getSymbolCategoryListKey(languageCode);
          await NiimbotSp().setString(saveKey, dataJson);
        }
        return result;
      }
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return null;
  }

  @override
  Future<T?> requestSymbolListFromNet<T>(
      {Map<String, dynamic>? params, ValueTransformer<T>? transformer, JsonTransformer<T>? jsonTransformer}) async {
    bool isNetworkConnect = await NetWorkListener.checkNetworkConnected();
    if (!isNetworkConnect) {
      return null;
    }
    try {
      // NiimbotHttpResponse<dynamic> netResult = await NiimbotHttpManager.getDefault()
      //     .getService<NiimbotRestService>()
      //     .client
      //     .post<dynamic>(SymbolApi.symbolList.path,
      //         data: params, options: NiimbotHttpRequestOptions(baseUrl: SymbolApi.symbolList.baseUrl));
      var request = NiimbotHttpRequest(
          path: SymbolApi.symbolList.path,
          method: "POST",
          data: params,
          options: NiimbotHttpRequestOptions(
              baseUrl: SymbolApi.symbolList.baseUrl,
              userAgent: NiimbotHttpManager.getDefault().getService<NiimbotRestService>().client.config.userAgent));
      NiimbotHttpResponse<dynamic> netResult =
          await NiimbotHttpManager.getDefault().getService<NiimbotRestService>().client.request<dynamic>(request);
      int? statusCode = netResult.statusCode;
      dynamic rawData = netResult.data?["data"];
      if ((statusCode == 200 || statusCode == 0 || statusCode == 1) && rawData != null) {
        if (transformer != null) {
          T? data = transformer.call(rawData["list"]);
          if (data != null) {
            // 获取符号分类ID
            var categoryId = params?['categoryId'];
            String saveKey = SymbolDefine.getSymbolListKey(categoryId);
            String groupJson = json.encode(data);
            await NiimbotSp().setString(saveKey, groupJson);
          }
          return data;
        }
        if (transformer == null && T is List<MaterialCategory>) {
          transformer ??= SymbolDefine.symbolItemTransformer as ValueTransformer<T>?;
        }
        T? data = transformer?.call(rawData["list"]);
        return data;
      }
    } catch (e, s) {
      debugPrint('Exception details:\n $e');
      debugPrint('Stack trace:\n $s');
    }
    return null;
  }

  @override
  Future<bool> clearSymbolCache() async {
    bool fail = false;
    var keys = NiimbotSp().getKeys();
    for (var key in keys) {
      if (key.startsWith(MaterialDefine.Category_List_Key_Prefix) ||
          key.startsWith(MaterialDefine.Material_Item_List_Key_Prefix) ||
          key.startsWith(MaterialDefine.Material_Item_List_Recent_Key) ||
          key.startsWith(MaterialDefine.Material_Item_List_Vip_Key) ||
          key.startsWith(MaterialDefine.Material_Item_Vip_Ids_Key)) {
        bool result = await NiimbotSp().remove(key);
        if (!result) {
          fail = true;
        }
      }
    }
    return !fail;
  }

  @override
  Future<bool> clearSymbolCategoryCache() async {
    bool fail = false;
    var keys = NiimbotSp().getKeys();
    for (var key in keys) {
      if (key.startsWith(MaterialDefine.Category_List_Key_Prefix)) {
        bool result = await NiimbotSp().remove(key);
        if (!result) {
          fail = true;
        }
      }
    }
    return !fail;
  }

  @override
  Future<bool> clearSymbolListCache() async {
    bool fail = false;
    var keys = NiimbotSp().getKeys();
    for (var key in keys) {
      if (key.startsWith(MaterialDefine.Material_Item_List_Key_Prefix) ||
          key.startsWith(MaterialDefine.Material_Item_List_Recent_Key) ||
          key.startsWith(MaterialDefine.Material_Item_List_Vip_Key)) {
        bool result = await NiimbotSp().remove(key);
        if (!result) {
          fail = true;
        }
      }
    }
    return !fail;
  }
}
