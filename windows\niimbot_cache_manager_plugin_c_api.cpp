#include "include/niimbot_cache_manager/niimbot_cache_manager_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "niimbot_cache_manager_plugin.h"

void NiimbotCacheManagerPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  niimbot_cache_manager::NiimbotCacheManagerPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
