// To parse this JSON data, do
//
//     final symbolCategory = symbolCategoryFromJson(jsonString);

import 'dart:convert';

SymbolCategory symbolCategoryFromJson(String str) => SymbolCategory.fromJson(json.decode(str));

String symbolCategoryToJson(SymbolCategory data) => json.encode(data.toJson());

/// 符号分类
class SymbolCategory {
  final String? id;
  final String? title;
  final int? sort;

  SymbolCategory({
    this.id,
    this.title,
    this.sort,
  });

  factory SymbolCategory.fromJson(Map<String, dynamic> json) => SymbolCategory(
        id: json["id"],
        title: json["title"],
        sort: json["sort"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "sort": sort,
      };
}
