import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_cache_manager/niimbot_cache_manager_platform_interface.dart';
import 'package:niimbot_cache_manager/niimbot_cache_manager_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockNiimbotCacheManagerPlatform
    with MockPlatformInterfaceMixin
    implements NiimbotCacheManagerPlatform {

  @override
  Future<String?> getPlatformVersion() => Future.value('42');
}

void main() {
  final NiimbotCacheManagerPlatform initialPlatform = NiimbotCacheManagerPlatform.instance;

  test('$MethodChannelNiimbotCacheManager is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelNiimbotCacheManager>());
  });

  test('getPlatformVersion', () async {
    NiimbotCacheManager niimbotCacheManagerPlugin = NiimbotCacheManager();
    MockNiimbotCacheManagerPlatform fakePlatform = MockNiimbotCacheManagerPlatform();
    NiimbotCacheManagerPlatform.instance = fakePlatform;

    expect(await niimbotCacheManagerPlugin.getPlatformVersion(), '42');
  });
}
