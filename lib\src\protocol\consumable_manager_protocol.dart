import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:tuple/tuple.dart';
import 'package:niimbot_cache_manager/src/model/consumable/consumable_model.dart';

/// [ConsumableManagerProtocol] Consumable cache protocol
/// Includes network requests, cache management, etc.
abstract class ConsumableManagerProtocol {
  /// Load consumable specifications from cache
  Future<T?> loadLocalConsumableSpecs<T>({
    required String consumableCode,
    ValueTransformer<T>? transformer,
  });

  /// Request consumable specifications from network
  Future<T?> requestConsumableSpecsFromNet<T>({
    required String consumableCode,
    ValueTransformer<T>? transformer,
    JsonTransformer<T>? jsonTransformer,
  });

  /// Save consumable specifications to cache
  Future<bool> saveConsumableSpecs<T>({
    required String consumableCode,
    required T specs,
    required JsonTransformer<T> jsonTransformer,
  });

  /// Save consumable data directly from a JSON string
  /// Used primarily for testing or direct data updates
  Future<bool> saveConsumableDataFromJson({
    required String consumableCode,
    required String jsonData,
  });

  /// Clear all consumable cache
  Future<bool> clearConsumableCache();

  /// Find a specific consumable specification by its ID within a consumable code
  /// Returns a tuple containing (ConsumableCategory?, ConsumableSpec?, bool)
  /// - ConsumableCategory: The category containing the found spec
  /// - ConsumableSpec: The specification matching the provided specId
  /// - bool: Indicates whether the spec was found
  Future<Tuple3<ConsumableCategory?, ConsumableSpec?, bool>>
  findConsumableSpecById({
    required String consumableCode,
    required String specId,
    bool preferLocalData = false,
  });
}
