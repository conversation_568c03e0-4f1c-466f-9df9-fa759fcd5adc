import 'dart:convert';

class GrayConfig {
  String? userId;
  String? applicationCode;
  List<ActiveBranches>? activeBranches;

  GrayConfig({
    this.userId,
    this.applicationCode,
    this.activeBranches,
  });

  GrayConfig.fromJson(dynamic json) {
    userId = json['userId'];
    applicationCode = json['applicationCode'];
    if (json['activeBranches'] != null) {
      activeBranches = [];
      json['activeBranches'].forEach((v) {
        activeBranches?.add(ActiveBranches.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['userId'] = userId;
    map['applicationCode'] = applicationCode;
    if (activeBranches != null) {
      map['activeBranches'] = activeBranches?.map((v) => v.toJson()).toList();
    }
    return map;
  }
}

ActiveBranches activeBranchesFromJson(String str) =>
    ActiveBranches.fromJson(json.decode(str));

String activeBranchesToJson(ActiveBranches data) => json.encode(data.toJson());

class ActiveBranches {
  String? branch;
  String? module;
  String? evaluatedAt;
  String? executionId;
  String? strategyId;

  ActiveBranches({
    this.branch,
    this.module,
    this.evaluatedAt,
    this.executionId,
    this.strategyId,
  });

  ActiveBranches.fromJson(dynamic json) {
    branch = json['branch'];
    module = json['module'];
    evaluatedAt = json['evaluatedAt'];
    executionId = json['executionId'];
    strategyId = json['strategyId'];
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['branch'] = branch;
    map['module'] = module;
    map['evaluatedAt'] = evaluatedAt;
    map['executionId'] = executionId;
    map['strategyId'] = strategyId;
    return map;
  }
}
