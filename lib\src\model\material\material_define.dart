import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_cache_manager/src/model/material/material_category.dart';
import 'package:niimbot_cache_manager/src/model/material/material_item.dart';

typedef RefreshGroupListVipStatus<T> = Function(
    {Map<String, dynamic>? arguments});

class MaterialDefine {
  static const String Category_List_Key_Prefix = "material_category_list";
  static const String Material_Item_List_Key_Prefix = "material_item_list";
  static const String Material_Item_List_Recent_Key =
      "material_item_list_recent";
  static const String Material_Item_List_Vip_Key = "material_item_list_vip";
  static const String Material_Item_Vip_Ids_Key = "material_item_vip_ids";

  /// 默认转换器
  static ValueTransformer<List<MaterialCategory>?> materialCategoryTransformer =
      (dynamic rawValue) {
    List<MaterialCategory>? result = [];
    if (rawValue is List<Map<String, dynamic>>) {
      for (var e in rawValue) {
        result.add(MaterialCategory.fromJson(e));
      }
    }
    return result;
  };

  /// 默认转换器
  static ValueTransformer<List<MaterialItem>?> materialItemTransformer =
      (dynamic rawValue) {
    List<MaterialItem>? result = [];
    if (rawValue is List<Map<String, dynamic>>) {
      for (var e in rawValue) {
        result.add(MaterialItem.fromJson(e));
      }
    }
    return result;
  };

  /// 保存素材分类的key
  static String getMaterialCategoryListKey(String languageCode, bool isBorder) {
    String language = languageCode.toLowerCase();
    String key;
    String suffix = isBorder ? "_boder" : "";
    if (language.isNotEmpty) {
      key = "${Category_List_Key_Prefix}_$language$suffix";
    } else {
      key = Category_List_Key_Prefix + suffix;
    }
    return key;
  }

  static String getMaterialVipIdsKey(bool isBorder) {
    return isBorder
        ? "${Material_Item_Vip_Ids_Key}_boder"
        : Material_Item_Vip_Ids_Key;
  }

  static String getItemListRecentKey(bool isBorder) {
    String suffix = isBorder ? "_boder" : "";
    return Material_Item_List_Recent_Key + suffix;
  }

  /// 保存素材列表的key
  static String getMaterialListKey(
      int industryId, int categoryId, bool isBorder) {
    String suffix = isBorder ? "_boder" : "";
    if (categoryId == -2) {
      return Material_Item_List_Vip_Key + suffix;
    }
    if (categoryId == -1) {
      return Material_Item_List_Recent_Key + suffix;
    }
    return "${Material_Item_List_Key_Prefix}_${industryId}_$categoryId$suffix";
  }
}
