import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:niimbot_cache_manager/niimbot_cache_manager_method_channel.dart';

void main() {
  MethodChannelNiimbotCacheManager platform = MethodChannelNiimbotCacheManager();
  const MethodChannel channel = MethodChannel('niimbot_cache_manager');

  TestWidgetsFlutterBinding.ensureInitialized();

  setUp(() {
    channel.setMockMethodCallHandler((MethodCall methodCall) async {
      return '42';
    });
  });

  tearDown(() {
    channel.setMockMethodCallHandler(null);
  });

  test('getPlatformVersion', () async {
    expect(await platform.getPlatformVersion(), '42');
  });
}
